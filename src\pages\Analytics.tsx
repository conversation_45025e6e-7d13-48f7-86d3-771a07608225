import { Card } from "@/components/ui/card";
import { <PERSON><PERSON><PERSON>, Line, ResponsiveContainer, XAxis, <PERSON><PERSON><PERSON><PERSON>, Toolt<PERSON> } from "recharts";

const analyticsData = [
  { name: "<PERSON>", value: 1200 },
  { name: "Feb", value: 2100 },
  { name: "<PERSON>", value: 800 },
  { name: "Apr", value: 1600 },
  { name: "May", value: 900 },
  { name: "<PERSON>", value: 1700 },
];

const Analytics = () => {
  return (
    <div className="space-y-8">
      <header>
        <h1 className="text-4xl font-bold text-primary">Analytics Overview</h1>
        <p className="text-secondary-foreground">Track your financial performance</p>
      </header>

      <Card className="glass-card p-6">
        <h3 className="text-lg font-semibold mb-4">Performance Trends</h3>
        <div className="h-[400px] w-full">
          <ResponsiveContainer width="100%" height="100%">
            <LineChart data={analyticsData}>
              <XAxis dataKey="name" stroke="#888888" />
              <YAxis stroke="#888888" />
              <Tooltip />
              <Line
                type="monotone"
                dataKey="value"
                stroke="#8989DE"
                strokeWidth={2}
                dot={false}
              />
            </LineChart>
          </ResponsiveContainer>
        </div>
      </Card>
    </div>
  );
};

export default Analytics;