import { useState, useEffect } from "react";
import { useNavigate, useParams } from "react-router-dom";
import { Card } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import { ArrowLeft } from "lucide-react";
import { toast } from "sonner";
import { supabase } from "@/integrations/supabase/client";
import { createClient } from '@supabase/supabase-js';

const supabaseAdmin = createClient(
  import.meta.env.VITE_SUPABASE_URL,
  import.meta.env.VITE_SUPABASE_SERVICE_ROLE_KEY
);

const AddTeamMember = () => {
  const navigate = useNavigate();
  const { id } = useParams();
  const isEditing = !!id;
  const [loading, setLoading] = useState(false);
  const [roles, setRoles] = useState<any[]>([]);
  const [formData, setFormData] = useState({
    email: "",
    password: "",
    role_id: "",
    refer_code: "",
    first_name: "",
    last_name: "",
    phone: "",
    address: "",
    city: "",
    state: "",
    pincode: "",
    country: "India",
    account_number: "",
    ifsc_code: "",
    bank_name: "",
    branch_name: "",
    commission_pct: "0"
  });

  useEffect(() => {
    fetchRoles();
    if (isEditing && id) {
      fetchTeamMember();
    }
  }, [isEditing, id]);

  const fetchTeamMember = async () => {
    try {
      const { data, error } = await supabase
        .from('team_members')
        .select('*')
        .eq('id', id)
        .single();

      if (error) throw error;

      setFormData({
        email: data.email || '',
        password: '',
        role_id: '',
        refer_code: data.refer_code || '',
        first_name: data.first_name || '',
        last_name: data.last_name || '',
        phone: data.phone || '',
        address: data.address || '',
        city: data.city || '',
        state: data.state || '',
        pincode: data.pincode || '',
        country: data.country || 'India',
        account_number: data.account_number || '',
        ifsc_code: data.ifsc_code || '',
        bank_name: data.bank_name || '',
        branch_name: data.branch_name || '',
        commission_pct: data.commission_pct?.toString() || '0'
      });
    } catch (error: any) {
      toast.error('Failed to fetch team member: ' + error.message);
    }
  };

  const fetchRoles = async () => {
    try {
      const { data, error } = await supabase
        .from('roles')
        .select('*')
        .eq('is_active', true)
        .neq('name', 'client');
      
      if (error) throw error;
      setRoles(data || []);
    } catch (error: any) {
      toast.error('Failed to fetch roles: ' + error.message);
    }
  };

  const generateReferCode = () => {
    const prefix = 'TM';
    const timestamp = Date.now().toString().slice(-6);
    const random = Math.random().toString(36).substring(2, 5).toUpperCase();
    return `${prefix}${timestamp}${random}`;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);

    try {
      if (isEditing) {
        const { data: currentMember } = await supabase
          .from('team_members')
          .select('email, user_id')
          .eq('id', id)
          .single();

        const { error: updateError } = await supabase
          .from('team_members')
          .update({
            first_name: formData.first_name,
            last_name: formData.last_name,
            email: formData.email,
            phone: formData.phone,
            address: formData.address,
            city: formData.city,
            state: formData.state,
            pincode: formData.pincode,
            country: formData.country,
            account_number: formData.account_number,
            ifsc_code: formData.ifsc_code,
            bank_name: formData.bank_name,
            branch_name: formData.branch_name,
            commission_pct: parseFloat(formData.commission_pct)
          })
          .eq('id', id);

        if (updateError) throw updateError;

        if (currentMember?.email !== formData.email && currentMember?.user_id) {
          const { error: authError } = await supabaseAdmin.auth.admin.updateUserById(
            currentMember.user_id,
            { email: formData.email }
          );
          
          if (authError) {
            console.warn('Failed to update auth email:', authError.message);
            toast.warning('Team member updated but email change failed. Please update manually.');
          }
        }

        if (currentMember?.user_id) {
          await supabase
            .from('users')
            .update({ 
              email: formData.email,
              first_name: formData.first_name,
              last_name: formData.last_name,
              mobile: formData.phone
            })
            .eq('id', currentMember.user_id);
        }

        toast.success("Team member updated successfully!");
      } else {
        const { data: authData, error: authError } = await supabaseAdmin.auth.admin.createUser({
          email: formData.email,
          password: formData.password,
          user_metadata: {
            first_name: formData.first_name,
            last_name: formData.last_name
          }
        });

        if (authError) throw authError;

        if (authData.user) {
          const { error: userError } = await supabase
            .from('users')
            .update({ 
              role_id: formData.role_id,
              first_name: formData.first_name,
              last_name: formData.last_name,
              mobile: formData.phone
            })
            .eq('id', authData.user.id);

          if (userError) throw userError;

          const { error: teamError } = await supabase
            .from('team_members')
            .insert({
              user_id: authData.user.id,
              refer_code: generateReferCode(),
              first_name: formData.first_name,
              last_name: formData.last_name,
              email: formData.email,
              phone: formData.phone,
              address: formData.address,
              city: formData.city,
              state: formData.state,
              pincode: formData.pincode,
              country: formData.country,
              account_number: formData.account_number,
              ifsc_code: formData.ifsc_code,
              bank_name: formData.bank_name,
              branch_name: formData.branch_name,
              commission_pct: parseFloat(formData.commission_pct)
            });

          if (teamError) throw teamError;

          toast.success("Team member created successfully!");
        }
      }
      
      navigate("/admin/team");
    } catch (error: any) {
      toast.error(`Failed to ${isEditing ? 'update' : 'create'} team member: ` + error.message);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50 p-6">
      <div className=" mx-auto space-y-8">
        <header className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <Button 
              variant="outline" 
              size="sm" 
              onClick={() => navigate("/admin/team")}
              className="shadow-sm hover:shadow-md transition-shadow"
            >
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Team
            </Button>
            <div>
              <h1 className="text-3xl font-bold bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent">
                {isEditing ? 'Edit Team Member' : 'Add New Team Member'}
              </h1>
              <p className="text-muted-foreground mt-1">
                {isEditing ? 'Update team member information' : 'Create a new team member with login credentials'}
              </p>
            </div>
          </div>
        </header>

        <Card className="shadow-xl border-0 bg-white/95 backdrop-blur-sm">
          <div className="p-8">
            <form onSubmit={handleSubmit} className="space-y-8">
              {/* Login Credentials */}
              <div className="space-y-4">
                <h3 className="text-lg font-semibold">{isEditing ? 'Account Information' : 'Login Credentials'}</h3>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="email">Email *</Label>
                    <Input
                      id="email"
                      type="email"
                      value={formData.email}
                      onChange={(e) => setFormData({...formData, email: e.target.value})}
                      placeholder="Enter email address"
                      required
                    />
                  </div>
                  {!isEditing && (
                    <div className="space-y-2">
                      <Label htmlFor="password">Password *</Label>
                      <Input
                        id="password"
                        type="password"
                        value={formData.password}
                        onChange={(e) => setFormData({...formData, password: e.target.value})}
                        placeholder="Enter password"
                        required
                      />
                    </div>
                  )}
                  {!isEditing && (
                    <div className="space-y-2">
                      <Label htmlFor="role_id">Role *</Label>
                      <Select value={formData.role_id} onValueChange={(value) => setFormData({...formData, role_id: value})}>
                        <SelectTrigger>
                          <SelectValue placeholder="Select role" />
                        </SelectTrigger>
                        <SelectContent>
                          {roles.map((role) => (
                            <SelectItem key={role.id} value={role.id}>
                              {role.name}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>
                  )}
                </div>
              </div>

              {/* Personal Information */}
              <div className="space-y-6">
                <div className="flex items-center space-x-3">
                  <div className="w-8 h-8 bg-gradient-to-r from-blue-500 to-indigo-500 rounded-lg flex items-center justify-center">
                    <span className="text-white font-semibold text-sm">1</span>
                  </div>
                  <h3 className="text-xl font-semibold text-gray-800">Personal Information</h3>
                </div>
                
                <div className="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-xl p-6">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div className="space-y-2">
                      <Label htmlFor="first_name" className="text-sm font-medium text-gray-700">First Name *</Label>
                      <Input
                        id="first_name"
                        value={formData.first_name}
                        onChange={(e) => setFormData({...formData, first_name: e.target.value})}
                        placeholder="Enter first name"
                        className="border-gray-300 focus:border-blue-500 focus:ring-blue-500/20"
                        required
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="last_name" className="text-sm font-medium text-gray-700">Last Name</Label>
                      <Input
                        id="last_name"
                        value={formData.last_name}
                        onChange={(e) => setFormData({...formData, last_name: e.target.value})}
                        placeholder="Enter last name"
                        className="border-gray-300 focus:border-blue-500 focus:ring-blue-500/20"
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="phone" className="text-sm font-medium text-gray-700">Phone Number *</Label>
                      <Input
                        id="phone"
                        value={formData.phone}
                        onChange={(e) => setFormData({...formData, phone: e.target.value})}
                        placeholder="Enter phone number"
                        className="border-gray-300 focus:border-blue-500 focus:ring-blue-500/20"
                        required
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="refer_code" className="text-sm font-medium text-gray-700">Refer Code</Label>
                      <Input
                        id="refer_code"
                        value={formData.refer_code}
                        onChange={(e) => setFormData({...formData, refer_code: e.target.value})}
                        placeholder={isEditing ? "Refer code" : "Auto-generated"}
                        className="border-gray-300 focus:border-blue-500 focus:ring-blue-500/20"
                        disabled={!isEditing}
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="commission_pct" className="text-sm font-medium text-gray-700">Commission Percentage</Label>
                      <Input
                        id="commission_pct"
                        type="number"
                        step="0.01"
                        min="0"
                        max="100"
                        value={formData.commission_pct}
                        onChange={(e) => setFormData({...formData, commission_pct: e.target.value})}
                        placeholder="Enter commission percentage"
                        className="border-gray-300 focus:border-blue-500 focus:ring-blue-500/20"
                      />
                    </div>
                  </div>
                </div>
              </div>

              {/* Address Information */}
              <div className="space-y-6">
                <div className="flex items-center space-x-3">
                  <div className="w-8 h-8 bg-gradient-to-r from-green-500 to-emerald-500 rounded-lg flex items-center justify-center">
                    <span className="text-white font-semibold text-sm">2</span>
                  </div>
                  <h3 className="text-xl font-semibold text-gray-800">Address Information</h3>
                </div>
                <div className="grid grid-cols-1 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="address">Address</Label>
                    <Textarea
                      id="address"
                      value={formData.address}
                      onChange={(e) => setFormData({...formData, address: e.target.value})}
                      placeholder="Enter full address"
                      rows={3}
                    />
                  </div>
                  <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="city">City</Label>
                      <Input
                        id="city"
                        value={formData.city}
                        onChange={(e) => setFormData({...formData, city: e.target.value})}
                        placeholder="Enter city"
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="state">State</Label>
                      <Input
                        id="state"
                        value={formData.state}
                        onChange={(e) => setFormData({...formData, state: e.target.value})}
                        placeholder="Enter state"
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="pincode">Pincode</Label>
                      <Input
                        id="pincode"
                        value={formData.pincode}
                        onChange={(e) => setFormData({...formData, pincode: e.target.value})}
                        placeholder="Enter pincode"
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="country">Country</Label>
                      <Input
                        id="country"
                        value={formData.country}
                        onChange={(e) => setFormData({...formData, country: e.target.value})}
                        placeholder="Enter country"
                      />
                    </div>
                  </div>
                </div>
              </div>

              {/* Bank Information */}
              <div className="space-y-6">
                <div className="flex items-center space-x-3">
                  <div className="w-8 h-8 bg-gradient-to-r from-purple-500 to-pink-500 rounded-lg flex items-center justify-center">
                    <span className="text-white font-semibold text-sm">3</span>
                  </div>
                  <h3 className="text-xl font-semibold text-gray-800">Bank Information</h3>
                </div>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="account_number">Account Number</Label>
                    <Input
                      id="account_number"
                      value={formData.account_number}
                      onChange={(e) => setFormData({...formData, account_number: e.target.value})}
                      placeholder="Enter account number"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="ifsc_code">IFSC Code</Label>
                    <Input
                      id="ifsc_code"
                      value={formData.ifsc_code}
                      onChange={(e) => setFormData({...formData, ifsc_code: e.target.value})}
                      placeholder="Enter IFSC code"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="bank_name">Bank Name</Label>
                    <Input
                      id="bank_name"
                      value={formData.bank_name}
                      onChange={(e) => setFormData({...formData, bank_name: e.target.value})}
                      placeholder="Enter bank name"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="branch_name">Branch Name</Label>
                    <Input
                      id="branch_name"
                      value={formData.branch_name}
                      onChange={(e) => setFormData({...formData, branch_name: e.target.value})}
                      placeholder="Enter branch name"
                    />
                  </div>
                </div>
              </div>

              <div className="flex justify-end space-x-4">
                <Button type="button" variant="outline" onClick={() => navigate("/admin/team")}>
                  Cancel
                </Button>
                <Button type="submit" disabled={loading}>
                  {loading ? (isEditing ? "Updating..." : "Creating...") : (isEditing ? "Update Team Member" : "Create Team Member")}
                </Button>
              </div>
            </form>
          </div>
        </Card>
      </div>
    </div>
  );
};

export default AddTeamMember;