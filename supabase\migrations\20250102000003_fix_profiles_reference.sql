-- Fix any remaining references to profiles table

-- Update the get_user_role RPC function to use correct table
CREATE OR REPLACE FUNCTION public.get_user_role(user_id UUID)
RETURNS TEXT
LANGUAGE sql
SECURITY DEFINER
SET search_path = public
AS $$
  SELECT r.name 
  FROM users u
  JOIN roles r ON u.role_id = r.id
  WHERE u.id = user_id
  AND u.is_active = true 
  AND u.is_deleted = false
  LIMIT 1;
$$;

-- Ensure the handle_new_user function is correct
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER AS $$
DECLARE
    client_role_id UUID;
BEGIN
    -- Get client role ID
    SELECT id INTO client_role_id FROM public.roles WHERE name = 'client';
    
    -- Insert into users table (not profiles)
    INSERT INTO public.users (id, email, role_id, created_at, updated_at)
    VALUES (NEW.id, NEW.email, client_role_id, now(), now())
    ON CONFLICT (id) DO UPDATE SET
        email = EXCLUDED.email,
        role_id = EXCLUDED.role_id,
        updated_at = now();
    
    -- Insert into user_roles table
    INSERT INTO public.user_roles (user_id, role_id, assigned_by, created_at, updated_at)
    VALUES (NEW.id, client_role_id, NEW.id, now(), now())
    ON CONFLICT (user_id, role_id) DO NOTHING;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;