import { useState, useEffect } from "react";
import { useNavigate, useParams } from "react-router-dom";
import { Card } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import { ArrowLeft, Upload, X } from "lucide-react";
import { toast } from "sonner";
import { supabase } from "@/integrations/supabase/client";
import { createClient } from '@supabase/supabase-js';

const supabaseAdmin = createClient(
  import.meta.env.VITE_SUPABASE_URL,
  import.meta.env.VITE_SUPABASE_SERVICE_ROLE_KEY
);

const AddClient = () => {
  const navigate = useNavigate();
  const { id } = useParams();
  const isEditing = !!id;
  const [loading, setLoading] = useState(false);
  const [roles, setRoles] = useState<any[]>([]);
  const [uploading, setUploading] = useState(false);
  const [photoPreview, setPhotoPreview] = useState<string>('');
  const [formData, setFormData] = useState({
    // Auth fields
    email: "",
    password: "",
    role_id: "",

    // Client fields
    first_name: "",
    last_name: "",
    mobile_number: "",
    aadhar_number: "",
    pan_card_number: "",
    address: "",
    city: "",
    state: "",
    pincode: "",
    country: "India",
    client_photo_url: "",
    account_number: "",
    ifsc_code: "",
    bank_name: "",
    branch_name: ""
  });

  useEffect(() => {
    fetchRoles();
    if (isEditing && id) {
      fetchClient();
    }
  }, [isEditing, id]);

  const fetchClient = async () => {
    try {
      const { data, error } = await supabase
        .from('clients')
        .select('*')
        .eq('id', id)
        .single();

      if (error) throw error;

      setFormData({
        email: data.email || '',
        password: '',
        role_id: '',
        first_name: data.first_name || '',
        last_name: data.last_name || '',
        mobile_number: data.mobile_number || '',
        aadhar_number: data.aadhar_number || '',
        pan_card_number: data.pan_card_number || '',
        address: data.address || '',
        city: data.city || '',
        state: data.state || '',
        pincode: data.pincode || '',
        country: data.country || 'India',
        client_photo_url: data.client_photo_url || '',
        account_number: data.account_number || '',
        ifsc_code: data.ifsc_code || '',
        bank_name: data.bank_name || '',
        branch_name: data.branch_name || ''
      });
      setPhotoPreview(data.client_photo_url || '');
    } catch (error: any) {
      toast.error('Failed to fetch client: ' + error.message);
    }
  };

  const handlePhotoUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    setUploading(true);
    try {
      const fileExt = file.name.split('.').pop();
      const fileName = `${Date.now()}.${fileExt}`;
      const filePath = `client-photos/${fileName}`;

      const { error: uploadError } = await supabase.storage
        .from('client-photos')
        .upload(filePath, file);

      if (uploadError) throw uploadError;

      const { data: { publicUrl } } = supabase.storage
        .from('client-photos')
        .getPublicUrl(filePath);

      setFormData({ ...formData, client_photo_url: publicUrl });
      setPhotoPreview(publicUrl);
      toast.success('Photo uploaded successfully!');
    } catch (error: any) {
      toast.error('Failed to upload photo: ' + error.message);
    } finally {
      setUploading(false);
    }
  };

  const removePhoto = () => {
    setFormData({ ...formData, client_photo_url: '' });
    setPhotoPreview('');
  };

  const fetchRoles = async () => {
    try {
      const { data, error } = await supabase
        .from('roles')
        .select('*')
        .eq('is_active', true);

      if (error) throw error;
      setRoles(data || []);
    } catch (error: any) {
      toast.error('Failed to fetch roles: ' + error.message);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);

    try {
      if (isEditing) {
        // Get current client data to check if email changed
        const { data: currentClient } = await supabase
          .from('clients')
          .select('email, user_id')
          .eq('id', id)
          .single();

        // Update client record
        const { error: clientError } = await supabase
          .from('clients')
          .update({
            first_name: formData.first_name,
            last_name: formData.last_name,
            email: formData.email,
            mobile_number: formData.mobile_number,
            aadhar_number: formData.aadhar_number,
            pan_card_number: formData.pan_card_number,
            address: formData.address,
            city: formData.city,
            state: formData.state,
            pincode: formData.pincode,
            country: formData.country,
            client_photo_url: formData.client_photo_url,
            account_number: formData.account_number,
            ifsc_code: formData.ifsc_code,
            bank_name: formData.bank_name,
            branch_name: formData.branch_name
          })
          .eq('id', id);

        if (clientError) throw clientError;

        // Update auth user email if it changed
        if (currentClient?.email !== formData.email && currentClient?.user_id) {
          const { error: authError } = await supabaseAdmin.auth.admin.updateUserById(
            currentClient.user_id,
            { email: formData.email }
          );

          if (authError) {
            console.warn('Failed to update auth email:', authError.message);
            toast.warning('Client updated but email change failed. Please update manually.');
          }
        }

        // Update users table email
        if (currentClient?.user_id) {
          await supabase
            .from('users')
            .update({
              email: formData.email,
              first_name: formData.first_name,
              last_name: formData.last_name,
              mobile: formData.mobile_number
            })
            .eq('id', currentClient.user_id);
        }

        toast.success("Client updated successfully!");
        navigate("/admin/clients");
      } else {
        // Create new client with auth user
        const { data: authData, error: authError } = await supabaseAdmin.auth.admin.createUser({
          email: formData.email,
          password: formData.password,
          user_metadata: {
            first_name: formData.first_name,
            last_name: formData.last_name
          }
        });

        if (authError) throw authError;

        if (authData.user) {
          // Update user role and info
          const { error: userError } = await supabase
            .from('users')
            .update({
              role_id: formData.role_id,
              first_name: formData.first_name,
              last_name: formData.last_name,
              mobile: formData.mobile_number
            })
            .eq('id', authData.user.id);

          if (userError) throw userError;

          // Create client record
          const { error: clientError } = await supabase
            .from('clients')
            .insert({
              user_id: authData.user.id,
              first_name: formData.first_name,
              last_name: formData.last_name,
              email: formData.email,
              mobile_number: formData.mobile_number,
              aadhar_number: formData.aadhar_number,
              pan_card_number: formData.pan_card_number,
              address: formData.address,
              city: formData.city,
              state: formData.state,
              pincode: formData.pincode,
              country: formData.country,
              client_photo_url: formData.client_photo_url,
              account_number: formData.account_number,
              ifsc_code: formData.ifsc_code,
              bank_name: formData.bank_name,
              branch_name: formData.branch_name
            });

          if (clientError) throw clientError;

          toast.success("Client created successfully!");
          navigate("/admin/clients");
        }
      }
    } catch (error: any) {
      toast.error(`Failed to ${isEditing ? 'update' : 'create'} client: ` + error.message);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50 p-6">
      <div className=" mx-auto space-y-8">
        <header className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <Button
              variant="outline"
              size="sm"
              onClick={() => navigate("/admin/clients")}
              className="shadow-sm hover:shadow-md transition-shadow"
            >
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Clients
            </Button>
            <div>
              <h1 className="text-3xl font-bold bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent">
                {isEditing ? 'Edit Client' : 'Add New Client'}
              </h1>
              <p className="text-muted-foreground mt-1">
                {isEditing ? 'Update client information' : 'Create a new client with login credentials'}
              </p>
            </div>
          </div>
        </header>

        <Card className="shadow-xl border-0 bg-white/95 backdrop-blur-sm">
          <div className="p-8">
            <form onSubmit={handleSubmit} className="space-y-8">
              {/* Login Credentials */}
              <div className="space-y-4">
                <h3 className="text-lg font-semibold">{isEditing ? 'Account Information' : 'Login Credentials'}</h3>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="email">Email *</Label>
                    <Input
                      id="email"
                      type="email"
                      value={formData.email}
                      onChange={(e) => setFormData({ ...formData, email: e.target.value })}
                      placeholder="Enter email address"
                      required
                    />
                  </div>
                  {!isEditing && (
                    <div className="space-y-2">
                      <Label htmlFor="password">Password *</Label>
                      <Input
                        id="password"
                        type="password"
                        value={formData.password}
                        onChange={(e) => setFormData({ ...formData, password: e.target.value })}
                        placeholder="Enter password"
                        required
                      />
                    </div>
                  )}
                  {!isEditing && (
                    <div className="space-y-2">
                      <Label htmlFor="role_id">Role *</Label>
                      <Select value={formData.role_id} onValueChange={(value) => setFormData({ ...formData, role_id: value })}>
                        <SelectTrigger>
                          <SelectValue placeholder="Select role" />
                        </SelectTrigger>
                        <SelectContent>
                          {roles.map((role) => (
                            <SelectItem key={role.id} value={role.id}>
                              {role.name}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>
                  )}
                </div>
              </div>

              {/* Personal Information */}
              <div className="space-y-6">
                <div className="flex items-center space-x-3">
                  <div className="w-8 h-8 bg-gradient-to-r from-blue-500 to-indigo-500 rounded-lg flex items-center justify-center">
                    <span className="text-white font-semibold text-sm">1</span>
                  </div>
                  <h3 className="text-xl font-semibold text-gray-800">Personal Information</h3>
                </div>

                <div className="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-xl p-6">
                  {/* Profile Photo Section */}
                  <div className="flex flex-col items-start space-y-4 mb-6">
                    <Label className="text-sm font-medium text-gray-700">Profile Photo</Label>
                    <div className="flex items-center space-x-6">
                      <div className="relative group">
                        {photoPreview ? (
                          <div className="relative">
                            <img
                              src={photoPreview}
                              alt="Profile"
                              className="w-24 h-24 rounded-xl object-cover border-4 border-white shadow-lg group-hover:shadow-xl transition-shadow"
                            />
                            <Button
                              type="button"
                              variant="destructive"
                              size="sm"
                              className="absolute -top-2 -right-2 w-7 h-7 rounded-full p-0 shadow-lg"
                              onClick={removePhoto}
                            >
                              <X className="h-4 w-4" />
                            </Button>
                          </div>
                        ) : (
                          <div className="w-24 h-24 rounded-xl bg-gradient-to-br from-gray-100 to-gray-200 flex items-center justify-center border-2 border-dashed border-gray-300 group-hover:border-blue-400 transition-colors">
                            <Upload className="h-10 w-10 text-gray-400 group-hover:text-blue-500 transition-colors" />
                          </div>
                        )}
                      </div>
                      <div className="flex-1">
                        <Input
                          type="file"
                          accept="image/*"
                          onChange={handlePhotoUpload}
                          disabled={uploading}
                          className="mb-2 border-2 border-dashed border-gray-300 hover:border-blue-400 transition-colors"
                        />
                        <p className="text-xs text-gray-500">
                          {uploading ? 'Uploading...' : 'Upload JPG, PNG or GIF (max 5MB)'}
                        </p>
                      </div>
                    </div>
                  </div>

                  {/* Form Fields */}
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div className="space-y-2">
                      <Label htmlFor="first_name" className="text-sm font-medium text-gray-700">First Name *</Label>
                      <Input
                        id="first_name"
                        value={formData.first_name}
                        onChange={(e) => setFormData({ ...formData, first_name: e.target.value })}
                        placeholder="Enter first name"
                        className="border-gray-300 focus:border-blue-500 focus:ring-blue-500/20"
                        required
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="last_name" className="text-sm font-medium text-gray-700">Last Name</Label>
                      <Input
                        id="last_name"
                        value={formData.last_name}
                        onChange={(e) => setFormData({ ...formData, last_name: e.target.value })}
                        placeholder="Enter last name"
                        className="border-gray-300 focus:border-blue-500 focus:ring-blue-500/20"
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="mobile_number" className="text-sm font-medium text-gray-700">Mobile Number *</Label>
                      <Input
                        id="mobile_number"
                        value={formData.mobile_number}
                        onChange={(e) => setFormData({ ...formData, mobile_number: e.target.value })}
                        placeholder="Enter mobile number"
                        className="border-gray-300 focus:border-blue-500 focus:ring-blue-500/20"
                        required
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="aadhar_number" className="text-sm font-medium text-gray-700">Aadhar Number</Label>
                      <Input
                        id="aadhar_number"
                        value={formData.aadhar_number}
                        onChange={(e) => setFormData({ ...formData, aadhar_number: e.target.value })}
                        placeholder="Enter Aadhar number"
                        className="border-gray-300 focus:border-blue-500 focus:ring-blue-500/20"
                      />
                    </div>
                    <div className="space-y-2 md:col-span-1">
                      <Label htmlFor="pan_card_number" className="text-sm font-medium text-gray-700">PAN Card Number</Label>
                      <Input
                        id="pan_card_number"
                        value={formData.pan_card_number}
                        onChange={(e) => setFormData({ ...formData, pan_card_number: e.target.value })}
                        placeholder="Enter PAN card number"
                        className="border-gray-300 focus:border-blue-500 focus:ring-blue-500/20"
                      />
                    </div>
                  </div>
                </div>
              </div>

              {/* Address Information */}
              <div className="space-y-6">
                <div className="flex items-center space-x-3">
                  <div className="w-8 h-8 bg-gradient-to-r from-green-500 to-emerald-500 rounded-lg flex items-center justify-center">
                    <span className="text-white font-semibold text-sm">2</span>
                  </div>
                  <h3 className="text-xl font-semibold text-gray-800">Address Information</h3>
                </div>
                <div className="grid grid-cols-1 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="address">Address</Label>
                    <Textarea
                      id="address"
                      value={formData.address}
                      onChange={(e) => setFormData({ ...formData, address: e.target.value })}
                      placeholder="Enter full address"
                      rows={3}
                    />
                  </div>
                  <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="city">City</Label>
                      <Input
                        id="city"
                        value={formData.city}
                        onChange={(e) => setFormData({ ...formData, city: e.target.value })}
                        placeholder="Enter city"
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="state">State</Label>
                      <Input
                        id="state"
                        value={formData.state}
                        onChange={(e) => setFormData({ ...formData, state: e.target.value })}
                        placeholder="Enter state"
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="pincode">Pincode</Label>
                      <Input
                        id="pincode"
                        value={formData.pincode}
                        onChange={(e) => setFormData({ ...formData, pincode: e.target.value })}
                        placeholder="Enter pincode"
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="country">Country</Label>
                      <Input
                        id="country"
                        value={formData.country}
                        onChange={(e) => setFormData({ ...formData, country: e.target.value })}
                        placeholder="Enter country"
                      />
                    </div>
                  </div>
                </div>
              </div>

              {/* Bank Information */}
              <div className="space-y-6">
                <div className="flex items-center space-x-3">
                  <div className="w-8 h-8 bg-gradient-to-r from-purple-500 to-pink-500 rounded-lg flex items-center justify-center">
                    <span className="text-white font-semibold text-sm">3</span>
                  </div>
                  <h3 className="text-xl font-semibold text-gray-800">Bank Information</h3>
                </div>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="account_number">Account Number</Label>
                    <Input
                      id="account_number"
                      value={formData.account_number}
                      onChange={(e) => setFormData({ ...formData, account_number: e.target.value })}
                      placeholder="Enter account number"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="ifsc_code">IFSC Code</Label>
                    <Input
                      id="ifsc_code"
                      value={formData.ifsc_code}
                      onChange={(e) => setFormData({ ...formData, ifsc_code: e.target.value })}
                      placeholder="Enter IFSC code"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="bank_name">Bank Name</Label>
                    <Input
                      id="bank_name"
                      value={formData.bank_name}
                      onChange={(e) => setFormData({ ...formData, bank_name: e.target.value })}
                      placeholder="Enter bank name"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="branch_name">Branch Name</Label>
                    <Input
                      id="branch_name"
                      value={formData.branch_name}
                      onChange={(e) => setFormData({ ...formData, branch_name: e.target.value })}
                      placeholder="Enter branch name"
                    />
                  </div>
                </div>
              </div>

              <div className="flex justify-end space-x-4">
                <Button type="button" variant="outline" onClick={() => navigate("/admin/clients")}>
                  Cancel
                </Button>
                <Button type="submit" disabled={loading}>
                  {loading ? (isEditing ? "Updating..." : "Creating...") : (isEditing ? "Update Client" : "Create Client")}
                </Button>
              </div>
            </form>
          </div>
        </Card>
      </div>
    </div>
  );
};

export default AddClient;