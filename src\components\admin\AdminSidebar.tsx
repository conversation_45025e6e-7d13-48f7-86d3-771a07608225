
import { Link, useLocation } from "react-router-dom";
import { cn } from "@/lib/utils";
import { useAuth } from "@/contexts/AuthContext";
import { 
  LayoutDashboard, 
  Users, 
  TrendingUp, 
  Banknote, 
  Building2,
  UserCheck,
  Calculator,
  PieChart,
  FileText,
  Bell,
  Settings,
  LogOut,
  Shield,
  Key
} from "lucide-react";

const getMenuItems = (role: string) => {
  const baseItems = [
    { icon: LayoutDashboard, label: "Dashboard", path: `/${role}` },
  ];
  
  if (role === 'admin') {
    return [
      ...baseItems,
      { icon: Users, label: "Clients", path: "/admin/clients" },
      { icon: TrendingUp, label: "Investments", path: "/admin/investments" },
      { icon: Banknote, label: "Schemes", path: "/admin/schemes" },
      { icon: Building2, label: "Broker Investments", path: "/admin/broker-investments" },
      { icon: User<PERSON>he<PERSON>, label: "Brokers", path: "/admin/brokers" },
      { icon: Users, label: "Team", path: "/admin/team" },
      { icon: Calculator, label: "Calculator", path: "/admin/calculator" },
      { icon: PieChart, label: "Portfolio", path: "/admin/portfolio" },
      { icon: FileText, label: "Blogs", path: "/admin/blogs" },
      { icon: Bell, label: "Alerts", path: "/admin/alerts" },
      { icon: Shield, label: "Roles", path: "/admin/roles" },
      { icon: Key, label: "Permissions", path: "/admin/permissions" },
      { icon: Settings, label: "Settings", path: "/admin/settings" },
    ];
  }
  
  if (role === 'team') {
    return [
      ...baseItems,
      { icon: Users, label: "Clients", path: "/team/clients" },
      { icon: TrendingUp, label: "Investments", path: "/team/investments" },
      { icon: Calculator, label: "Calculator", path: "/team/calculator" },
      { icon: PieChart, label: "Portfolio", path: "/team/portfolio" },
      { icon: Settings, label: "Settings", path: "/team/settings" },
    ];
  }
  
  // Client role
  return [
    ...baseItems,
    { icon: TrendingUp, label: "Investments", path: "/client/investments" },
    { icon: PieChart, label: "Portfolio", path: "/client/portfolio" },
    { icon: Settings, label: "Settings", path: "/client/settings" },
  ];
};

interface AdminSidebarProps {
  isOpen?: boolean;
  onClose?: () => void;
}

const AdminSidebar = ({ isOpen = false, onClose }: AdminSidebarProps) => {
  const location = useLocation();
  const { user, logout } = useAuth();
  const menuItems = getMenuItems(user?.role || 'admin');

  return (
    <div className={`fixed left-0 top-0 h-full w-64 bg-card border-r border-border shadow-sm z-50 transform transition-transform duration-300 lg:translate-x-0 ${isOpen ? 'translate-x-0' : '-translate-x-full'} lg:static lg:transform-none`}>
      <div className="flex flex-col h-full">
        {/* Logo Section */}
        <div className="p-6 border-b border-border">
          <div className="flex items-center gap-3">
            <div className="h-10 w-10 bg-primary rounded-lg flex items-center justify-center">
              <span className="text-primary-foreground font-bold text-lg">CC</span>
            </div>
            <div>
              <h2 className="text-lg font-bold text-foreground">Care Capital</h2>
              <p className="text-xs text-muted-foreground">
                {user?.role === 'admin' ? 'Admin Panel' : 
                 user?.role === 'team' ? 'Team Panel' : 'Client Panel'}
              </p>
            </div>
          </div>
        </div>
        
        {/* Navigation */}
        <nav className="flex-1 px-3 py-4 overflow-y-auto">
          <ul className="space-y-1">
            {menuItems.map((item) => {
              const Icon = item.icon;
              const isActive = location.pathname === item.path;
              
              return (
                <li key={item.path}>
                  <Link
                    to={item.path}
                    onClick={onClose}
                    className={cn(
                      "flex items-center gap-3 px-3 py-2.5 rounded-lg transition-all duration-200 group",
                      "hover:bg-accent/50 hover:text-accent-foreground",
                      isActive 
                        ? "bg-primary text-primary-foreground shadow-sm" 
                        : "text-muted-foreground hover:text-foreground"
                    )}
                  >
                    <Icon className={cn(
                      "h-5 w-5 transition-colors",
                      isActive ? "text-primary-foreground" : "group-hover:text-foreground"
                    )} />
                    <span className="text-sm font-medium">{item.label}</span>
                  </Link>
                </li>
              );
            })}
          </ul>
        </nav>
      </div>
    </div>
  );
};

export default AdminSidebar;
