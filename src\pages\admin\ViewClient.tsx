import { useState, useEffect } from "react";
import { use<PERSON>ara<PERSON>, useNavigate } from "react-router-dom";
import { Card } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { ArrowLeft, Edit, Mail, Phone, MapPin, CreditCard, Building2 } from "lucide-react";
import { toast } from "sonner";
import { supabase } from "@/integrations/supabase/client";

const ViewClient = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const [client, setClient] = useState<any>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    if (id) {
      fetchClient();
    }
  }, [id]);

  const fetchClient = async () => {
    try {
      const { data, error } = await supabase
        .from('clients')
        .select(`
          *,
          investments(amount, status, created_at),
          nominees(name, relationship)
        `)
        .eq('id', id)
        .single();

      if (error) throw error;

      const clientWithStats = {
        ...data,
        name: `${data.first_name} ${data.last_name || ''}`.trim(),
        totalInvestment: data.investments?.reduce((sum: number, inv: any) => sum + (inv.amount || 0), 0) || 0,
        activeSchemes: data.investments?.length || 0,
        status: data.is_active ? 'Active' : 'Inactive',
        joinDate: new Date(data.created_at).toLocaleDateString()
      };

      setClient(clientWithStats);
    } catch (error: any) {
      toast.error('Failed to fetch client: ' + error.message);
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-lg">Loading client...</div>
      </div>
    );
  }

  if (!client) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-lg">Client not found</div>
      </div>
    );
  }

  return (
    <div className="space-y-8">
      <header className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Button variant="outline" size="sm" onClick={() => navigate("/admin/clients")}>
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Clients
          </Button>
          <div>
            <h1 className="text-4xl font-bold text-primary">{client.name}</h1>
            <p className="text-muted-foreground">Client Details</p>
          </div>
        </div>
        <Button onClick={() => navigate(`/admin/clients/edit/${client.id}`)}>
          <Edit className="h-4 w-4 mr-2" />
          Edit Client
        </Button>
      </header>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        {/* Personal Information */}
        <Card className="p-6">
          <h3 className="text-lg font-semibold mb-4">Personal Information</h3>
          <div className="space-y-4">
            <div className="flex items-center space-x-3">
              {client.client_photo_url ? (
                <img 
                  src={client.client_photo_url} 
                  alt={client.name}
                  className="h-12 w-12 rounded-full object-cover border-2 border-gray-200"
                />
              ) : (
                <div className="h-12 w-12 rounded-full bg-primary text-white flex items-center justify-center">
                  {client.name.charAt(0)}
                </div>
              )}
              <div>
                <p className="font-medium">{client.name}</p>
                <p className="text-sm text-muted-foreground">{client.client_code || 'No code'}</p>
              </div>
            </div>
            
            <div className="space-y-2">
              <div className="flex items-center space-x-2">
                <Mail className="h-4 w-4 text-muted-foreground" />
                <span className="text-sm">{client.email || 'No email'}</span>
              </div>
              <div className="flex items-center space-x-2">
                <Phone className="h-4 w-4 text-muted-foreground" />
                <span className="text-sm">{client.mobile_number}</span>
              </div>
              <div className="flex items-center space-x-2">
                <MapPin className="h-4 w-4 text-muted-foreground" />
                <span className="text-sm">{client.address || 'No address'}</span>
              </div>
            </div>

            <div className="pt-4 border-t">
              <p className="text-sm"><strong>PAN:</strong> {client.pan_card_number || 'Not provided'}</p>
              <p className="text-sm"><strong>Aadhar:</strong> {client.aadhar_number || 'Not provided'}</p>
              <p className="text-sm"><strong>City:</strong> {client.city || 'Not provided'}</p>
              <p className="text-sm"><strong>State:</strong> {client.state || 'Not provided'}</p>
              <p className="text-sm"><strong>Country:</strong> {client.country}</p>
            </div>
          </div>
        </Card>

        {/* Investment Summary */}
        <Card className="p-6">
          <h3 className="text-lg font-semibold mb-4">Investment Summary</h3>
          <div className="space-y-4">
            <div className="text-center p-4 bg-primary/10 rounded-lg">
              <p className="text-2xl font-bold text-primary">₹{client.totalInvestment.toLocaleString()}</p>
              <p className="text-sm text-muted-foreground">Total Investment</p>
            </div>
            
            <div className="grid grid-cols-2 gap-4">
              <div className="text-center p-3 bg-gray-50 rounded-lg">
                <p className="text-xl font-semibold">{client.activeSchemes}</p>
                <p className="text-xs text-muted-foreground">Active Schemes</p>
              </div>
              <div className="text-center p-3 bg-gray-50 rounded-lg">
                <Badge variant={client.status === 'Active' ? 'default' : 'secondary'}>
                  {client.status}
                </Badge>
                <p className="text-xs text-muted-foreground mt-1">Status</p>
              </div>
            </div>

            <div className="pt-4 border-t">
              <p className="text-sm"><strong>Join Date:</strong> {client.joinDate}</p>
              {client.nominees?.length > 0 && (
                <p className="text-sm"><strong>Nominee:</strong> {client.nominees[0].name} ({client.nominees[0].relationship})</p>
              )}
            </div>
          </div>
        </Card>

        {/* Bank Information */}
        <Card className="p-6">
          <h3 className="text-lg font-semibold mb-4">Bank Information</h3>
          <div className="space-y-4">
            <div className="flex items-center space-x-2">
              <Building2 className="h-4 w-4 text-muted-foreground" />
              <span className="text-sm">{client.bank_name || 'Not provided'}</span>
            </div>
            <div className="flex items-center space-x-2">
              <CreditCard className="h-4 w-4 text-muted-foreground" />
              <span className="text-sm">{client.account_number || 'Not provided'}</span>
            </div>
            
            <div className="space-y-2 pt-4 border-t">
              <p className="text-sm"><strong>IFSC Code:</strong> {client.ifsc_code || 'Not provided'}</p>
              <p className="text-sm"><strong>Branch:</strong> {client.branch_name || 'Not provided'}</p>
            </div>
          </div>
        </Card>
      </div>

      {/* Recent Investments */}
      {client.investments?.length > 0 && (
        <Card className="p-6">
          <h3 className="text-lg font-semibold mb-4">Recent Investments</h3>
          <div className="space-y-2">
            {client.investments.slice(0, 5).map((investment: any, index: number) => (
              <div key={index} className="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
                <div>
                  <p className="font-medium">₹{investment.amount.toLocaleString()}</p>
                  <p className="text-sm text-muted-foreground">
                    {new Date(investment.created_at).toLocaleDateString()}
                  </p>
                </div>
                <Badge variant={investment.status === 'active' ? 'default' : 'secondary'}>
                  {investment.status}
                </Badge>
              </div>
            ))}
          </div>
        </Card>
      )}
    </div>
  );
};

export default ViewClient;