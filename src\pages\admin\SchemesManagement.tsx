
import { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { Card, CardContent, CardHeader } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { 
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { Switch } from "@/components/ui/switch";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { Skeleton } from "@/components/ui/skeleton";
import { 
  PlusCircle, 
  Search, 
  Edit, 
  Trash2,
  MoreHorizontal,
  Eye,
  Percent,
  Calendar,
  FileDown,
  TrendingUp
} from "lucide-react";
import { toast } from "sonner";
import { supabase } from "@/integrations/supabase/client";



const SchemesManagement = () => {
  const navigate = useNavigate();
  const [schemes, setSchemes] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState("");
  const [schemeToDelete, setSchemeToDelete] = useState<any | null>(null);

  useEffect(() => {
    fetchSchemes();
  }, []);

  const fetchSchemes = async () => {
    setLoading(true);
    try {
      const { data, error } = await supabase
        .from('schemes')
        .select('*')
        .eq('is_deleted', false)
        .order('created_at', { ascending: false });

      if (error) throw error;

      setSchemes(data || []);
    } catch (error: any) {
      toast.error('Failed to fetch schemes: ' + error.message);
    } finally {
      setLoading(false);
    }
  };

  const toggleSchemeStatus = async (id: string, currentStatus: boolean) => {
    try {
      const { error } = await supabase
        .from('schemes')
        .update({ is_active: !currentStatus })
        .eq('id', id);

      if (error) throw error;

      setSchemes(schemes.map(scheme => 
        scheme.id === id ? { ...scheme, is_active: !currentStatus } : scheme
      ));
      toast.success("Scheme status updated!");
    } catch (error: any) {
      toast.error('Failed to update scheme status: ' + error.message);
    }
  };

  const handleDeleteScheme = async () => {
    if (!schemeToDelete) return;
    try {
      const { error } = await supabase
        .from('schemes')
        .update({ is_deleted: true })
        .eq('id', schemeToDelete.id);

      if (error) throw error;

      toast.success(`Scheme "${schemeToDelete.name}" has been deleted.`);
      setSchemes(schemes.filter(s => s.id !== schemeToDelete.id));
      setSchemeToDelete(null);
    } catch (error: any) {
      toast.error('Failed to delete scheme: ' + error.message);
    }
  };

  const filteredSchemes = schemes.filter(scheme =>
    scheme.name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
    scheme.description?.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const SkeletonRow = () => (
    <TableRow>
      <TableCell><Skeleton className="h-4 w-32" /></TableCell>
      <TableCell><Skeleton className="h-4 w-16" /></TableCell>
      <TableCell><Skeleton className="h-4 w-20" /></TableCell>
      <TableCell><Skeleton className="h-4 w-24" /></TableCell>
      <TableCell><Skeleton className="h-4 w-16" /></TableCell>
      <TableCell><Skeleton className="h-6 w-16 rounded-full" /></TableCell>
      <TableCell><Skeleton className="h-6 w-16 rounded-full" /></TableCell>
      <TableCell><Skeleton className="h-8 w-8" /></TableCell>
    </TableRow>
  );

  return (
    <div className="space-y-6">
      <div className="mx-auto">
        <header className="flex flex-col sm:flex-row justify-between sm:items-center gap-4">
          <div>
            <h1 className="text-2xl sm:text-3xl font-bold tracking-tight text-gray-800">Schemes Management</h1>
            <p className="text-sm text-gray-500 mt-1">Manage investment schemes and their parameters.</p>
          </div>
          <Button onClick={() => navigate('/admin/schemes/add')} className="shadow-sm w-full sm:w-auto">
            <PlusCircle className="h-4 w-4 mr-2" />
            Add New Scheme
          </Button>
        </header>

        <Card className="shadow-sm mt-4">
          <CardHeader>
            <div className="flex flex-col sm:flex-row justify-between sm:items-center gap-4">
              <div className="relative flex-1 md:grow-0">
                <Search className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-gray-400" />
                <Input
                  placeholder="Search schemes..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10 w-full md:w-80"
                />
              </div>
              <div className="flex items-center gap-2">
                <Button variant="outline" size="sm">
                  <FileDown className="h-4 w-4 mr-2" />
                  Export
                </Button>
              </div>
            </div>
          </CardHeader>
          <CardContent>

            <div className="rounded-md border">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Scheme Name</TableHead>
                    <TableHead>Interest Rate</TableHead>
                    <TableHead>Tenure</TableHead>
                    <TableHead>Investment Range</TableHead>
                    <TableHead className="hidden sm:table-cell">Lock-in</TableHead>
                    <TableHead className="hidden lg:table-cell">Status</TableHead>
                    <TableHead><span className="sr-only">Actions</span></TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {loading ? (
                    Array.from({ length: 5 }).map((_, i) => <SkeletonRow key={i} />)
                  ) : filteredSchemes.length > 0 ? (
                    filteredSchemes.map((scheme) => (
                      <TableRow key={scheme.id} className="hover:bg-gray-50">
                        <TableCell>
                          <div>
                            <p className="font-semibold text-gray-800">{scheme.name}</p>
                            <p className="text-sm text-gray-500 truncate">{scheme.description}</p>
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center space-x-1">
                            <TrendingUp className="h-4 w-4 text-green-600" />
                            <span className="font-medium text-green-600">{scheme.monthly_interest_pct}%</span>
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center space-x-1">
                            <Calendar className="h-4 w-4 text-blue-600" />
                            <span>{scheme.tenure_months} months</span>
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="text-sm">
                            <div className="font-medium">₹{scheme.min_investment?.toLocaleString()}</div>
                            <div className="text-gray-500">to ₹{scheme.max_investment?.toLocaleString()}</div>
                          </div>
                        </TableCell>
                        <TableCell className="hidden sm:table-cell">
                          <span className="text-sm">{scheme.lock_in_period || 0} months</span>
                        </TableCell>
                        <TableCell className="hidden lg:table-cell">
                          <div className="flex items-center space-x-2">
                            <Switch
                              checked={scheme.is_active}
                              onCheckedChange={() => toggleSchemeStatus(scheme.id, scheme.is_active)}
                            />
                            <Badge variant={scheme.is_active ? "default" : "secondary"}>
                              {scheme.is_active ? "Active" : "Inactive"}
                            </Badge>
                          </div>
                        </TableCell>
                        <TableCell>
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button variant="ghost" className="h-8 w-8 p-0">
                                <MoreHorizontal className="h-4 w-4" />
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="end">
                              <DropdownMenuItem onClick={() => navigate(`/admin/schemes/view/${scheme.id}`)}>
                                <Eye className="mr-2 h-4 w-4" />
                                View
                              </DropdownMenuItem>
                              <DropdownMenuItem onClick={() => navigate(`/admin/schemes/edit/${scheme.id}`)}>
                                <Edit className="mr-2 h-4 w-4" />
                                Edit
                              </DropdownMenuItem>
                              <DropdownMenuSeparator />
                              <DropdownMenuItem
                                onClick={() => setSchemeToDelete(scheme)}
                                className="text-red-600"
                              >
                                <Trash2 className="mr-2 h-4 w-4" />
                                Delete
                              </DropdownMenuItem>
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </TableCell>
                      </TableRow>
                    ))
                  ) : (
                    <TableRow>
                      <TableCell colSpan={7} className="h-48 text-center">
                        <div className="flex flex-col items-center justify-center gap-4">
                          <TrendingUp className="h-12 w-12 text-gray-400" />
                          <h3 className="text-xl font-semibold text-gray-700">No Schemes Found</h3>
                          <p className="text-gray-500">No schemes match your search. Try a different query.</p>
                        </div>
                      </TableCell>
                    </TableRow>
                  )}
                </TableBody>
              </Table>
            </div>
          </CardContent>
        </Card>
      </div>

      <AlertDialog open={!!schemeToDelete} onOpenChange={() => setSchemeToDelete(null)}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Are you absolutely sure?</AlertDialogTitle>
            <AlertDialogDescription>
              This action will mark the scheme "{schemeToDelete?.name}" as deleted. This is a soft delete and the data can be recovered.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction onClick={handleDeleteScheme} className="bg-red-600 hover:bg-red-700">
              Yes, Delete Scheme
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
};

export default SchemesManagement;
