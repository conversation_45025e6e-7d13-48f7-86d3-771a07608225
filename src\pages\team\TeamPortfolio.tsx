
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON><PERSON>, Pie, Cell, ResponsiveContainer, BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, Legend } from "recharts";

const TeamPortfolio = () => {
  const portfolioData = [
    { name: "Gold Plan", value: 40, color: "#8B5CF6" },
    { name: "Silver Plan", value: 35, color: "#06B6D4" },
    { name: "Platinum Plan", value: 25, color: "#10B981" },
  ];

  const performanceData = [
    { month: "Jan", clients: 2, investments: 75000 },
    { month: "Feb", clients: 3, investments: 125000 },
    { month: "Mar", clients: 2, investments: 50000 },
    { month: "Apr", clients: 4, investments: 200000 },
    { month: "May", clients: 1, investments: 25000 },
    { month: "Jun", clients: 3, investments: 150000 },
  ];

  return (
    <div className="space-y-6">
      <h1 className="text-3xl font-bold">Client Portfolio Overview</h1>
      
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Client Portfolio</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">₹6,25,000</div>
            <p className="text-xs text-muted-foreground">+8.2% from last month</p>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Active Clients</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">12</div>
            <p className="text-xs text-muted-foreground">Referred by you</p>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Avg. Returns</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">10.8%</div>
            <p className="text-xs text-muted-foreground">Annual percentage</p>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Commission Earned</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">₹15,625</div>
            <p className="text-xs text-muted-foreground">This month</p>
          </CardContent>
        </Card>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle>Client Portfolio Distribution</CardTitle>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <PieChart>
                <Pie
                  data={portfolioData}
                  cx="50%"
                  cy="50%"
                  labelLine={false}
                  label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                  outerRadius={80}
                  fill="#8884d8"
                  dataKey="value"
                >
                  {portfolioData.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={entry.color} />
                  ))}
                </Pie>
                <Tooltip />
              </PieChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Monthly Performance</CardTitle>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <BarChart data={performanceData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="month" />
                <YAxis />
                <Tooltip />
                <Legend />
                <Bar dataKey="clients" fill="#8B5CF6" name="New Clients" />
                <Bar dataKey="investments" fill="#06B6D4" name="Investments (₹)" />
              </BarChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Client Investment Summary</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {[
              { client: "Alice Johnson", scheme: "Gold Plan", amount: "₹75,000", returns: "12.5%", status: "Active" },
              { client: "Bob Wilson", scheme: "Silver Plan", amount: "₹50,000", returns: "10.2%", status: "Active" },
              { client: "Carol Davis", scheme: "Platinum Plan", amount: "₹1,25,000", returns: "15.8%", status: "Active" },
            ].map((investment, index) => (
              <div key={index} className="flex items-center justify-between p-4 border rounded-lg">
                <div>
                  <h3 className="font-semibold">{investment.client}</h3>
                  <p className="text-sm text-muted-foreground">{investment.scheme}</p>
                </div>
                <div className="text-right">
                  <div className="font-semibold">{investment.amount}</div>
                  <div className="text-sm text-green-600">{investment.returns} returns</div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default TeamPortfolio;
