
import { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { Card, CardContent, CardHeader } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { Skeleton } from "@/components/ui/skeleton";
import { PlusCircle, Search, Edit, Trash2, Eye, MoreH<PERSON>zontal, TrendingUp, FileDown } from "lucide-react";
import { toast } from "sonner";
import { supabase } from "@/integrations/supabase/client";

const InvestmentManagement = () => {
  const navigate = useNavigate();
  const [investments, setInvestments] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState("");
  const [investmentToDelete, setInvestmentToDelete] = useState<any | null>(null);

  useEffect(() => {
    fetchInvestments();
  }, []);

  const fetchInvestments = async () => {
    setLoading(true);
    try {
      const { data, error } = await supabase
        .from('investments')
        .select(`
          *,
          clients(first_name, last_name, email),
          schemes(name, monthly_interest_pct)
        `)
        .eq('is_deleted', false)
        .order('created_at', { ascending: false });

      if (error) throw error;

      const investmentsWithDetails = data?.map(investment => ({
        ...investment,
        client_name: `${investment.clients.first_name} ${investment.clients.last_name}`,
        scheme_name: investment.schemes.name,
        monthly_interest_pct: investment.schemes.monthly_interest_pct,
        status_badge: investment.status === 'active' ? 'Active' : 
                     investment.status === 'completed' ? 'Completed' : 
                     investment.status === 'closed' ? 'Closed' : 'Suspended'
      })) || [];

      setInvestments(investmentsWithDetails);
    } catch (error: any) {
      toast.error('Failed to fetch investments: ' + error.message);
    } finally {
      setLoading(false);
    }
  };

  const handleDeleteInvestment = async () => {
    if (!investmentToDelete) return;
    try {
      const { error } = await supabase
        .from('investments')
        .update({ is_deleted: true })
        .eq('id', investmentToDelete.id);

      if (error) throw error;

      toast.success(`Investment "${investmentToDelete.investment_code}" has been deleted.`);
      setInvestments(investments.filter(i => i.id !== investmentToDelete.id));
      setInvestmentToDelete(null);
    } catch (error: any) {
      toast.error('Failed to delete investment: ' + error.message);
    }
  };

  const filteredInvestments = investments.filter(investment =>
    investment.client_name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
    investment.scheme_name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
    investment.investment_code?.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const SkeletonRow = () => (
    <TableRow>
      <TableCell><Skeleton className="h-4 w-32" /></TableCell>
      <TableCell><Skeleton className="h-4 w-24" /></TableCell>
      <TableCell><Skeleton className="h-4 w-20" /></TableCell>
      <TableCell><Skeleton className="h-4 w-16" /></TableCell>
      <TableCell><Skeleton className="h-4 w-20" /></TableCell>
      <TableCell><Skeleton className="h-6 w-16 rounded-full" /></TableCell>
      <TableCell><Skeleton className="h-8 w-8" /></TableCell>
    </TableRow>
  );

  return (
    <div className="space-y-6">
      <div className="mx-auto">
        <header className="flex flex-col sm:flex-row justify-between sm:items-center gap-4">
          <div>
            <h1 className="text-2xl sm:text-3xl font-bold tracking-tight text-gray-800">Investment Management</h1>
            <p className="text-sm text-gray-500 mt-1">Manage all client investments and track performance.</p>
          </div>
          <Button onClick={() => navigate('/admin/investments/add')} className="shadow-sm w-full sm:w-auto">
            <PlusCircle className="h-4 w-4 mr-2" />
            Add New Investment
          </Button>
        </header>

        <Card className="shadow-sm mt-4">
          <CardHeader>
            <div className="flex flex-col sm:flex-row justify-between sm:items-center gap-4">
              <div className="relative flex-1 md:grow-0">
                <Search className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-gray-400" />
                <Input
                  placeholder="Search investments..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10 w-full md:w-80"
                />
              </div>
              <div className="flex items-center gap-2">
                <Button variant="outline" size="sm">
                  <FileDown className="h-4 w-4 mr-2" />
                  Export
                </Button>
              </div>
            </div>
          </CardHeader>
          <CardContent>
            <div className="rounded-md border">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Investment Code</TableHead>
                    <TableHead>Client</TableHead>
                    <TableHead>Scheme</TableHead>
                    <TableHead className="text-right">Amount</TableHead>
                    <TableHead className="hidden sm:table-cell">Start Date</TableHead>
                    <TableHead className="hidden lg:table-cell">End Date</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead><span className="sr-only">Actions</span></TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {loading ? (
                    Array.from({ length: 5 }).map((_, i) => <SkeletonRow key={i} />)
                  ) : filteredInvestments.length > 0 ? (
                    filteredInvestments.map((investment) => (
                      <TableRow key={investment.id} className="hover:bg-gray-50">
                        <TableCell>
                          <div>
                            <p className="font-medium text-gray-800">{investment.investment_code}</p>
                            <p className="text-xs text-gray-500">₹{investment.monthly_interest?.toLocaleString()}/month</p>
                          </div>
                        </TableCell>
                        <TableCell>
                          <div>
                            <p className="font-medium">{investment.client_name}</p>
                            <p className="text-sm text-gray-500">{investment.clients.email}</p>
                          </div>
                        </TableCell>
                        <TableCell>
                          <div>
                            <p className="font-medium">{investment.scheme_name}</p>
                            <p className="text-sm text-green-600">{investment.monthly_interest_pct}% monthly</p>
                          </div>
                        </TableCell>
                        <TableCell className="text-right">
                          <span className="font-mono font-medium">₹{investment.amount.toLocaleString()}</span>
                        </TableCell>
                        <TableCell className="hidden sm:table-cell">
                          <span className="text-sm">{new Date(investment.start_date).toLocaleDateString()}</span>
                        </TableCell>
                        <TableCell className="hidden lg:table-cell">
                          <span className="text-sm">{new Date(investment.end_date).toLocaleDateString()}</span>
                        </TableCell>
                        <TableCell>
                          <Badge variant={
                            investment.status === 'active' ? 'default' : 
                            investment.status === 'completed' ? 'secondary' : 
                            'outline'
                          }>
                            {investment.status_badge}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button variant="ghost" className="h-8 w-8 p-0">
                                <MoreHorizontal className="h-4 w-4" />
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="end">
                              <DropdownMenuItem onClick={() => navigate(`/admin/investments/view/${investment.id}`)}>
                                <Eye className="mr-2 h-4 w-4" />
                                View
                              </DropdownMenuItem>
                              <DropdownMenuItem onClick={() => navigate(`/admin/investments/edit/${investment.id}`)}>
                                <Edit className="mr-2 h-4 w-4" />
                                Edit
                              </DropdownMenuItem>
                              <DropdownMenuSeparator />
                              <DropdownMenuItem
                                onClick={() => setInvestmentToDelete(investment)}
                                className="text-red-600"
                              >
                                <Trash2 className="mr-2 h-4 w-4" />
                                Delete
                              </DropdownMenuItem>
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </TableCell>
                      </TableRow>
                    ))
                  ) : (
                    <TableRow>
                      <TableCell colSpan={8} className="h-48 text-center">
                        <div className="flex flex-col items-center justify-center gap-4">
                          <TrendingUp className="h-12 w-12 text-gray-400" />
                          <h3 className="text-xl font-semibold text-gray-700">No Investments Found</h3>
                          <p className="text-gray-500">No investments match your search. Try a different query.</p>
                        </div>
                      </TableCell>
                    </TableRow>
                  )}
                </TableBody>
              </Table>
            </div>
          </CardContent>
        </Card>
      </div>

      <AlertDialog open={!!investmentToDelete} onOpenChange={() => setInvestmentToDelete(null)}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Are you absolutely sure?</AlertDialogTitle>
            <AlertDialogDescription>
              This action will mark the investment "{investmentToDelete?.investment_code}" as deleted. This is a soft delete and the data can be recovered.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction onClick={handleDeleteInvestment} className="bg-red-600 hover:bg-red-700">
              Yes, Delete Investment
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
};

export default InvestmentManagement;
