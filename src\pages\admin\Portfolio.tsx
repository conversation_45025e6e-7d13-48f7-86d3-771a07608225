
import { <PERSON>, Card<PERSON>ontent, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON><PERSON>, Pie, Cell, ResponsiveContainer, BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, Legend } from "recharts";

const Portfolio = () => {
  const portfolioData = [
    { name: "Gold Plan", value: 45, color: "#8B5CF6" },
    { name: "Silver Plan", value: 30, color: "#06B6D4" },
    { name: "Platinum Plan", value: 25, color: "#10B981" },
  ];

  const performanceData = [
    { month: "Jan", returns: 8.5, target: 10 },
    { month: "Feb", returns: 12.3, target: 10 },
    { month: "Mar", returns: 9.8, target: 10 },
    { month: "Apr", returns: 15.2, target: 10 },
    { month: "May", returns: 11.5, target: 10 },
    { month: "Jun", returns: 13.8, target: 10 },
  ];

  return (
    <div className="space-y-6">
      <h1 className="text-3xl font-bold">Portfolio Overview</h1>
      
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Portfolio Value</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">₹2,45,67,890</div>
            <p className="text-xs text-muted-foreground">+12.5% from last month</p>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Active Investments</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">156</div>
            <p className="text-xs text-muted-foreground">Across all schemes</p>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Average Returns</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">11.2%</div>
            <p className="text-xs text-muted-foreground">Annual percentage</p>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Maturity This Month</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">₹18,45,000</div>
            <p className="text-xs text-muted-foreground">12 investments</p>
          </CardContent>
        </Card>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle>Portfolio Distribution</CardTitle>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <PieChart>
                <Pie
                  data={portfolioData}
                  cx="50%"
                  cy="50%"
                  labelLine={false}
                  label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                  outerRadius={80}
                  fill="#8884d8"
                  dataKey="value"
                >
                  {portfolioData.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={entry.color} />
                  ))}
                </Pie>
                <Tooltip />
              </PieChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Performance vs Target</CardTitle>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <BarChart data={performanceData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="month" />
                <YAxis />
                <Tooltip />
                <Legend />
                <Bar dataKey="returns" fill="#8B5CF6" name="Actual Returns %" />
                <Bar dataKey="target" fill="#06B6D4" name="Target Returns %" />
              </BarChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Top Performing Schemes</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {[
              { scheme: "Platinum Plan", returns: "15.2%", investors: 45, amount: "₹89,50,000" },
              { scheme: "Gold Plan", returns: "12.8%", investors: 78, amount: "₹1,25,30,000" },
              { scheme: "Silver Plan", returns: "9.5%", investors: 156, amount: "₹67,80,000" },
            ].map((scheme, index) => (
              <div key={index} className="flex items-center justify-between p-4 border rounded-lg">
                <div>
                  <h3 className="font-semibold">{scheme.scheme}</h3>
                  <p className="text-sm text-muted-foreground">{scheme.investors} investors</p>
                </div>
                <div className="text-right">
                  <div className="font-semibold text-green-600">{scheme.returns}</div>
                  <div className="text-sm text-muted-foreground">{scheme.amount}</div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default Portfolio;
