
import { Link, useLocation } from "react-router-dom";
import { cn } from "@/lib/utils";
import { useAuth } from "@/contexts/AuthContext";
import { 
  LayoutDashboard, 
  Users, 
  TrendingUp, 
  Calculator,
  Pie<PERSON><PERSON>,
  Settings,
  LogOut
} from "lucide-react";

const menuItems = [
  { icon: LayoutDashboard, label: "Dashboard", path: "/team" },
  { icon: Users, label: "My Clients", path: "/team/clients" },
  { icon: TrendingUp, label: "Investments", path: "/team/investments" },
  { icon: Calculator, label: "Calculator", path: "/team/calculator" },
  { icon: Pie<PERSON>hart, label: "Portfolio", path: "/team/portfolio" },
  { icon: Settings, label: "Settings", path: "/team/settings" },
];

const TeamSidebar = () => {
  const location = useLocation();
  const { user, logout } = useAuth();

  return (
    <div className="fixed left-0 top-0 h-full w-64 bg-white border-r border-gray-200 shadow-lg">
      <div className="flex flex-col h-full">
        <div className="p-6 border-b">
          <h2 className="text-2xl font-bold text-primary">Care Capital</h2>
          <p className="text-sm text-muted-foreground">Team Panel</p>
        </div>
        
        <nav className="flex-1 px-4 py-4">
          <ul className="space-y-2">
            {menuItems.map((item) => {
              const Icon = item.icon;
              const isActive = location.pathname === item.path;
              
              return (
                <li key={item.path}>
                  <Link
                    to={item.path}
                    className={cn(
                      "flex items-center gap-3 px-4 py-3 rounded-lg transition-all duration-200",
                      "hover:bg-primary/10",
                      isActive ? "bg-primary text-white" : "text-gray-600"
                    )}
                  >
                    <Icon className="h-5 w-5" />
                    <span>{item.label}</span>
                  </Link>
                </li>
              );
            })}
          </ul>
        </nav>

        <div className="p-4 border-t">
          <div className="flex items-center gap-3 px-4 py-3 mb-2">
            <div className="h-10 w-10 rounded-full bg-primary text-white flex items-center justify-center">
              {user?.name.charAt(0)}
            </div>
            <div className="flex flex-col">
              <span className="text-sm font-medium">{user?.name}</span>
              <span className="text-xs text-muted-foreground">{user?.email}</span>
            </div>
          </div>
          <button
            onClick={logout}
            className="flex items-center gap-3 px-4 py-3 w-full text-left hover:bg-red-50 hover:text-red-600 rounded-lg transition-colors"
          >
            <LogOut className="h-5 w-5" />
            <span>Logout</span>
          </button>
        </div>
      </div>
    </div>
  );
};

export default TeamSidebar;
