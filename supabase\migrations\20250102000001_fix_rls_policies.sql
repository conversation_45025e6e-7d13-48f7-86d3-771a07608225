-- Fix RLS policies to prevent infinite recursion

-- Drop existing problematic policies
DROP POLICY IF EXISTS "<PERSON><PERSON> can manage roles" ON public.roles;
DROP POLICY IF EXISTS "Ad<PERSON> can manage permissions" ON public.permissions;
DROP POLICY IF EXISTS "Ad<PERSON> can manage role permissions" ON public.role_permissions;
DROP POLICY IF EXISTS "Ad<PERSON> can view all users" ON public.users;
DROP POLICY IF EXISTS "Admins can manage all users" ON public.users;
DROP POLICY IF EXISTS "Ad<PERSON> can manage user roles" ON public.user_roles;

-- Create simple, non-recursive policies
CREATE POLICY "Allow service role full access to roles" ON public.roles
FOR ALL TO service_role
USING (true);

CREATE POLICY "Allow authenticated users to view roles" ON public.roles
FOR SELECT TO authenticated
USING (true);

CREATE POLICY "Allow service role full access to permissions" ON public.permissions
FOR ALL TO service_role
USING (true);

CREATE POLICY "Allow authenticated users to view permissions" ON public.permissions
FOR SELECT TO authenticated
USING (true);

CREATE POLICY "Allow service role full access to role_permissions" ON public.role_permissions
FOR ALL TO service_role
USING (true);

CREATE POLICY "Allow authenticated users to view role_permissions" ON public.role_permissions
FOR SELECT TO authenticated
USING (true);

CREATE POLICY "Allow service role full access to users" ON public.users
FOR ALL TO service_role
USING (true);

CREATE POLICY "Users can view own data" ON public.users
FOR SELECT TO authenticated
USING (id = auth.uid());

CREATE POLICY "Allow service role full access to user_roles" ON public.user_roles
FOR ALL TO service_role
USING (true);

CREATE POLICY "Users can view own roles" ON public.user_roles
FOR SELECT TO authenticated
USING (user_id = auth.uid());