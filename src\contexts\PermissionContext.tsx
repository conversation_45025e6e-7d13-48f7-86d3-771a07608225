import { createContext, useContext, ReactNode } from 'react';
import { usePermission, UserRole, Permission } from '@/hooks/usePermission';

interface PermissionContextType {
  userRole: UserRole;
  permissions: Permission;
  hasPermission: (permission: keyof Permission) => boolean;
  updateUserRole: (role: UserRole) => void;
  isAdmin: boolean;
  isTeamMember: boolean;
}

const PermissionContext = createContext<PermissionContextType | undefined>(undefined);

export const PermissionProvider = ({ children }: { children: ReactNode }) => {
  const permissionData = usePermission();

  return (
    <PermissionContext.Provider value={permissionData}>
      {children}
    </PermissionContext.Provider>
  );
};

export const usePermissionContext = () => {
  const context = useContext(PermissionContext);
  if (!context) {
    throw new Error('usePermissionContext must be used within PermissionProvider');
  }
  return context;
};