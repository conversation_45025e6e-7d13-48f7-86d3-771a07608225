import { useState, useEffect } from "react";
import { usePara<PERSON>, useNavigate } from "react-router-dom";
import { Card } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { ArrowLeft, Edit, TrendingUp, Calendar, DollarSign, Users, CheckCircle, Clock } from "lucide-react";
import { toast } from "sonner";
import { supabase } from "@/integrations/supabase/client";

const ViewInvestment = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const [investment, setInvestment] = useState<any>(null);
  const [payments, setPayments] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    if (id) {
      fetchInvestment();
      fetchPayments();
    }
  }, [id]);

  const fetchInvestment = async () => {
    try {
      const { data, error } = await supabase
        .from('investments')
        .select(`
          *,
          clients(first_name, last_name, email, mobile_number),
          schemes(name, monthly_interest_pct, tenure_months)
        `)
        .eq('id', id)
        .single();

      if (error) throw error;

      const investmentWithDetails = {
        ...data,
        client_name: `${data.clients.first_name} ${data.clients.last_name}`,
        scheme_snapshot: JSON.parse(data.scheme_snapshot || '{}'),
        team_members_data: JSON.parse(data.team_members || '[]'),
        status_badge: data.status === 'active' ? 'Active' : 
                     data.status === 'completed' ? 'Completed' : 
                     data.status === 'closed' ? 'Closed' : 'Suspended',
        created_date: new Date(data.created_at).toLocaleDateString()
      };

      setInvestment(investmentWithDetails);
    } catch (error: any) {
      toast.error('Failed to fetch investment: ' + error.message);
    } finally {
      setLoading(false);
    }
  };

  const fetchPayments = async () => {
    try {
      const { data, error } = await supabase
        .from('investment_payments')
        .select('*')
        .eq('investment_id', id)
        .order('month_number', { ascending: true });

      if (error) throw error;
      setPayments(data || []);
    } catch (error: any) {
      toast.error('Failed to fetch payments: ' + error.message);
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-lg">Loading investment...</div>
      </div>
    );
  }

  if (!investment) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-lg">Investment not found</div>
      </div>
    );
  }

  const paidPayments = payments.filter(p => p.status === 'paid').length;
  const totalPayments = payments.length;
  const progressPercentage = totalPayments > 0 ? (paidPayments / totalPayments) * 100 : 0;

  return (
    <div className="space-y-8">
      <header className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Button variant="outline" size="sm" onClick={() => navigate("/admin/investments")}>
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Investments
          </Button>
          <div>
            <h1 className="text-4xl font-bold text-primary">{investment.investment_code}</h1>
            <p className="text-muted-foreground">Investment Details</p>
          </div>
        </div>
        <Button onClick={() => navigate(`/admin/investments/edit/${investment.id}`)}>
          <Edit className="h-4 w-4 mr-2" />
          Edit Investment
        </Button>
      </header>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        {/* Investment Overview */}
        <Card className="p-6">
          <h3 className="text-lg font-semibold mb-4">Investment Overview</h3>
          <div className="space-y-4">
            <div className="text-center p-4 bg-primary/10 rounded-lg">
              <div className="flex items-center justify-center space-x-2 mb-2">
                <DollarSign className="h-5 w-5 text-primary" />
                <span className="text-sm text-muted-foreground">Investment Amount</span>
              </div>
              <p className="text-2xl font-bold text-primary">₹{investment.amount.toLocaleString()}</p>
            </div>
            
            <div className="space-y-3">
              <div className="flex justify-between">
                <span className="text-sm text-muted-foreground">Client:</span>
                <span className="font-medium">{investment.client_name}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm text-muted-foreground">Scheme:</span>
                <span className="font-medium">{investment.schemes.name}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm text-muted-foreground">Status:</span>
                <Badge variant={investment.status === 'active' ? 'default' : 'secondary'}>
                  {investment.status_badge}
                </Badge>
              </div>
              <div className="flex justify-between">
                <span className="text-sm text-muted-foreground">Created:</span>
                <span className="text-sm font-medium">{investment.created_date}</span>
              </div>
            </div>
          </div>
        </Card>

        {/* Returns & Timeline */}
        <Card className="p-6">
          <h3 className="text-lg font-semibold mb-4">Returns & Timeline</h3>
          <div className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div className="text-center p-3 bg-green-50 rounded-lg">
                <div className="flex items-center justify-center space-x-1 mb-1">
                  <TrendingUp className="h-4 w-4 text-green-600" />
                </div>
                <p className="text-lg font-semibold text-green-600">₹{investment.monthly_interest?.toLocaleString()}</p>
                <p className="text-xs text-muted-foreground">Monthly Interest</p>
              </div>
              <div className="text-center p-3 bg-blue-50 rounded-lg">
                <div className="flex items-center justify-center space-x-1 mb-1">
                  <Calendar className="h-4 w-4 text-blue-600" />
                </div>
                <p className="text-lg font-semibold text-blue-600">{investment.schemes.tenure_months}</p>
                <p className="text-xs text-muted-foreground">Months</p>
              </div>
            </div>
            
            <div className="space-y-3 pt-4 border-t">
              <div className="flex justify-between">
                <span className="text-sm text-muted-foreground">Start Date:</span>
                <span className="font-medium">{new Date(investment.start_date).toLocaleDateString()}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm text-muted-foreground">End Date:</span>
                <span className="font-medium">{new Date(investment.end_date).toLocaleDateString()}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm text-muted-foreground">Total Returns:</span>
                <span className="font-bold text-green-600">₹{investment.total_expected_return?.toLocaleString()}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm text-muted-foreground">Maturity Amount:</span>
                <span className="font-bold text-blue-600">₹{investment.maturity_amount?.toLocaleString()}</span>
              </div>
            </div>
          </div>
        </Card>

        {/* Team Members & Commission */}
        <Card className="p-6">
          <h3 className="text-lg font-semibold mb-4">Team Members & Commission</h3>
          <div className="space-y-4">
            {investment.team_members_data.length > 0 ? (
              <>
                <div className="text-center p-4 bg-purple-50 rounded-lg">
                  <div className="flex items-center justify-center space-x-2 mb-2">
                    <Users className="h-5 w-5 text-purple-600" />
                    <span className="text-sm text-muted-foreground">Total Commission</span>
                  </div>
                  <p className="text-xl font-bold text-purple-600">
                    ₹{((investment.amount * investment.total_commission_pct) / 100).toLocaleString()}
                  </p>
                </div>
                
                <div className="space-y-2">
                  {investment.team_members_data.map((member: any, index: number) => (
                    <div key={index} className="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
                      <div>
                        <p className="font-medium text-sm">{member.name}</p>
                        <p className="text-xs text-gray-500">{member.refer_code}</p>
                      </div>
                      <span className="font-medium text-purple-600">₹{member.commission_amount?.toLocaleString()}</span>
                    </div>
                  ))}
                </div>
              </>
            ) : (
              <div className="text-center py-8">
                <Users className="h-12 w-12 text-gray-400 mx-auto mb-2" />
                <p className="text-gray-500">No team members assigned</p>
              </div>
            )}
          </div>
        </Card>
      </div>

      {/* Payment Schedule */}
      <Card className="p-6">
        <div className="flex justify-between items-center mb-6">
          <h3 className="text-lg font-semibold">Payment Schedule</h3>
          <div className="flex items-center space-x-4">
            <div className="text-sm text-gray-600">
              Progress: {paidPayments}/{totalPayments} payments
            </div>
            <div className="w-32 bg-gray-200 rounded-full h-2">
              <div 
                className="bg-green-500 h-2 rounded-full transition-all duration-300"
                style={{ width: `${progressPercentage}%` }}
              ></div>
            </div>
          </div>
        </div>
        
        <div className="rounded-md border">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Month</TableHead>
                <TableHead>Due Date</TableHead>
                <TableHead className="text-right">Amount</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Paid Date</TableHead>
                <TableHead>Payment Method</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {payments.map((payment) => (
                <TableRow key={payment.id} className="hover:bg-gray-50">
                  <TableCell>
                    <div className="flex items-center space-x-2">
                      <span className="font-medium">Month {payment.month_number}</span>
                    </div>
                  </TableCell>
                  <TableCell>
                    <span className="text-sm">{new Date(payment.due_date).toLocaleDateString()}</span>
                  </TableCell>
                  <TableCell className="text-right">
                    <span className="font-mono font-medium">₹{payment.amount.toLocaleString()}</span>
                  </TableCell>
                  <TableCell>
                    <Badge variant={
                      payment.status === 'paid' ? 'default' : 
                      payment.status === 'overdue' ? 'destructive' : 
                      'secondary'
                    }>
                      <div className="flex items-center space-x-1">
                        {payment.status === 'paid' ? (
                          <CheckCircle className="h-3 w-3" />
                        ) : (
                          <Clock className="h-3 w-3" />
                        )}
                        <span className="capitalize">{payment.status}</span>
                      </div>
                    </Badge>
                  </TableCell>
                  <TableCell>
                    <span className="text-sm">
                      {payment.paid_at ? new Date(payment.paid_at).toLocaleDateString() : '-'}
                    </span>
                  </TableCell>
                  <TableCell>
                    <span className="text-sm capitalize">
                      {payment.payment_method || '-'}
                    </span>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>
      </Card>
    </div>
  );
};

export default ViewInvestment;