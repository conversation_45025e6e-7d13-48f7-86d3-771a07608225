import { Outlet } from "react-router-dom";
import { useState, useEffect } from "react";
import { useAuth } from "@/contexts/AuthContext";
import DynamicSidebar from "@/components/shared/DynamicSidebar";
import DynamicTopNavbar from "@/components/shared/DynamicTopNavbar";

const DashboardLayout = () => {
  const { user } = useAuth();
  const [userRole, setUserRole] = useState<string>('client');
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // Use mock role from auth context for now
    if (user?.role) {
      setUserRole(user.role);
    }
    setLoading(false);
  }, [user]);

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-lg">Loading...</div>
      </div>
    );
  }

  return (
    <div className="flex min-h-screen bg-background">
      <DynamicSidebar userRole={userRole} />
      <div className="flex-1 ml-64">
        <DynamicTopNavbar userRole={userRole} />
        <main className="p-6">
          <div className="mx-auto">
            <Outlet />
          </div>
        </main>
      </div>
    </div>
  );
};

export default DashboardLayout;