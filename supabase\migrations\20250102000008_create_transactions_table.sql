-- Create transactions table for tracking all financial transactions

CREATE TABLE public.transactions (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  transaction_code character varying UNIQUE,
  investment_id uuid,
  client_id uuid NOT NULL,
  transaction_type transaction_type NOT NULL,
  amount numeric NOT NULL,
  description text,
  transaction_date timestamp with time zone DEFAULT now(),
  reference_number character varying,
  payment_method character varying,
  status character varying DEFAULT 'completed',
  remarks text,
  is_active boolean DEFAULT true,
  is_deleted boolean DEFAULT false,
  created_at timestamp with time zone DEFAULT now(),
  updated_at timestamp with time zone DEFAULT now(),
  created_by uuid,
  updated_by uuid,
  CONSTRAINT transactions_pkey PRIMARY KEY (id),
  CONSTRAINT transactions_investment_id_fkey FOREIGN KEY (investment_id) REFERENCES public.investments(id),
  CONSTRAINT transactions_client_id_fkey FOREIGN KEY (client_id) REFERENCES public.clients(id),
  CONSTRAINT transactions_created_by_fkey FOREIGN KEY (created_by) REFERENCES auth.users(id),
  CONSTRAINT transactions_updated_by_fkey FOREIGN KEY (updated_by) REFERENCES auth.users(id)
);

-- Create indexes
CREATE INDEX idx_transactions_client_id ON public.transactions(client_id);
CREATE INDEX idx_transactions_investment_id ON public.transactions(investment_id);
CREATE INDEX idx_transactions_transaction_date ON public.transactions(transaction_date);
CREATE INDEX idx_transactions_transaction_type ON public.transactions(transaction_type);
CREATE INDEX idx_transactions_status ON public.transactions(status);

-- Enable RLS
ALTER TABLE public.transactions ENABLE ROW LEVEL SECURITY;

-- Create RLS policy
CREATE POLICY "Enable read for authenticated users" ON public.transactions FOR SELECT USING (auth.role() = 'authenticated');
CREATE POLICY "Enable insert for authenticated users" ON public.transactions FOR INSERT WITH CHECK (auth.role() = 'authenticated');
CREATE POLICY "Enable update for authenticated users" ON public.transactions FOR UPDATE USING (auth.role() = 'authenticated');

-- Create trigger for updated_at
CREATE TRIGGER update_transactions_updated_at BEFORE UPDATE ON public.transactions FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();