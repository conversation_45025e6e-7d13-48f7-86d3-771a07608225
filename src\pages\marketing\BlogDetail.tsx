
import { Card, CardContent } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { Calendar, User, Clock, Share2, Facebook, Twitter, Linkedin, ArrowLeft } from "lucide-react";
import { Link, useParams } from "react-router-dom";

const BlogDetail = () => {
  const { slug } = useParams();

  // In a real application, you would fetch the blog post based on the slug
  const blogPost = {
    id: 1,
    title: "5 Investment Strategies for 2024",
    content: `
      <div class="prose max-w-none">
        <p>As we navigate through 2024, the investment landscape continues to evolve with new opportunities and challenges. Whether you're a seasoned investor or just starting your journey, having the right strategies can make all the difference in achieving your financial goals.</p>
        
        <h2>1. Diversification Beyond Traditional Assets</h2>
        <p>While stocks and bonds remain fundamental to any portfolio, 2024 presents unique opportunities to diversify into alternative investments. Consider allocating a portion of your portfolio to:</p>
        <ul>
          <li>Real Estate Investment Trusts (REITs)</li>
          <li>Commodity investments</li>
          <li>International markets</li>
          <li>Sector-specific ETFs</li>
        </ul>
        
        <h2>2. Technology-Driven Investment Approaches</h2>
        <p>The rise of fintech has democratized investing, making it easier than ever to access sophisticated investment tools. Robo-advisors, AI-powered analytics, and automated rebalancing can help optimize your portfolio performance while reducing emotional decision-making.</p>
        
        <h2>3. Focus on Sustainable and ESG Investments</h2>
        <p>Environmental, Social, and Governance (ESG) investing is no longer just a trend—it's becoming a standard practice. Companies with strong ESG profiles often demonstrate better long-term performance and risk management.</p>
        
        <h2>4. Dollar-Cost Averaging in Volatile Markets</h2>
        <p>Market volatility can be your friend when you use dollar-cost averaging. By investing a fixed amount regularly, you automatically buy more shares when prices are low and fewer when prices are high, potentially reducing your average cost per share over time.</p>
        
        <h2>5. Emergency Fund First, Then Invest</h2>
        <p>Before diving into investments, ensure you have an emergency fund covering 3-6 months of expenses. This financial cushion prevents you from having to liquidate investments during market downturns or personal financial difficulties.</p>
        
        <h2>Conclusion</h2>
        <p>Successful investing in 2024 requires a balanced approach that combines traditional wisdom with modern tools and techniques. Remember that every investor's situation is unique, and what works for one person may not work for another.</p>
        
        <p>At Care Capital, we're committed to helping you navigate these strategies and find the investment approach that best suits your goals and risk tolerance. Our team of experts is always available to provide personalized guidance tailored to your specific needs.</p>
      </div>
    `,
    author: "Rajesh Kumar",
    date: "2024-01-15",
    category: "Investment Tips",
    tags: ["Investment", "Strategy", "2024"],
    readTime: "5 min read"
  };

  const relatedPosts = [
    {
      id: 2,
      title: "Understanding Market Volatility: A Beginner's Guide",
      excerpt: "Market volatility can be intimidating for new investors. Learn how to navigate volatile markets.",
      date: "2024-01-20"
    },
    {
      id: 3,
      title: "Gold vs Silver Investment Plans: Which is Right for You?",
      excerpt: "Compare our Gold and Silver investment plans to understand which option aligns best with your goals.",
      date: "2024-01-10"
    },
    {
      id: 4,
      title: "Building Your Emergency Fund: A Step-by-Step Guide",
      excerpt: "Learn why every investor needs an emergency fund and how to build one.",
      date: "2024-01-25"
    }
  ];

  return (
    <div className="min-h-screen">
      {/* Back Navigation */}
      <section className="py-6 border-b">
        <div className="container mx-auto px-6">
          <Link to="/blogs">
            <Button variant="ghost" className="mb-4">
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back to Articles
            </Button>
          </Link>
        </div>
      </section>

      {/* Article Header */}
      <section className="py-12">
        <div className="container mx-auto px-6">
          <div className="max-w-4xl mx-auto">
            <Badge className="mb-4">{blogPost.category}</Badge>
            <h1 className="text-4xl md:text-5xl font-bold mb-6">{blogPost.title}</h1>
            
            <div className="flex flex-wrap items-center gap-6 text-muted-foreground mb-8">
              <div className="flex items-center">
                <User className="h-4 w-4 mr-2" />
                <span>{blogPost.author}</span>
              </div>
              <div className="flex items-center">
                <Calendar className="h-4 w-4 mr-2" />
                <span>{blogPost.date}</span>
              </div>
              <div className="flex items-center">
                <Clock className="h-4 w-4 mr-2" />
                <span>{blogPost.readTime}</span>
              </div>
            </div>

            {/* Share Buttons */}
            <div className="flex items-center gap-4 mb-8">
              <span className="text-sm font-medium">Share:</span>
              <Button size="sm" variant="outline">
                <Facebook className="h-4 w-4 mr-2" />
                Facebook
              </Button>
              <Button size="sm" variant="outline">
                <Twitter className="h-4 w-4 mr-2" />
                Twitter
              </Button>
              <Button size="sm" variant="outline">
                <Linkedin className="h-4 w-4 mr-2" />
                LinkedIn
              </Button>
              <Button size="sm" variant="outline">
                <Share2 className="h-4 w-4" />
              </Button>
            </div>

            {/* Tags */}
            <div className="flex flex-wrap gap-2 mb-8">
              {blogPost.tags.map((tag, index) => (
                <Badge key={index} variant="secondary">
                  {tag}
                </Badge>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Article Content */}
      <section className="pb-12">
        <div className="container mx-auto px-6">
          <div className="max-w-4xl mx-auto">
            <div className="h-64 md:h-96 bg-gradient-to-r from-primary/20 to-primary/40 rounded-lg flex items-center justify-center mb-8">
              <span className="text-6xl">📈</span>
            </div>
            
            <div 
              className="prose prose-lg max-w-none"
              dangerouslySetInnerHTML={{ __html: blogPost.content }}
            />
          </div>
        </div>
      </section>

      {/* Author Bio */}
      <section className="py-12 bg-muted/30">
        <div className="container mx-auto px-6">
          <div className="max-w-4xl mx-auto">
            <Card>
              <CardContent className="p-8">
                <div className="flex items-start space-x-6">
                  <div className="w-20 h-20 bg-primary/10 rounded-full flex items-center justify-center">
                    <span className="text-2xl font-bold text-primary">RK</span>
                  </div>
                  <div className="flex-1">
                    <h3 className="text-xl font-semibold mb-2">{blogPost.author}</h3>
                    <p className="text-muted-foreground mb-4">
                      Founder & CEO at Care Capital with over 20 years of experience in financial services. 
                      Rajesh specializes in investment strategy, wealth management, and helping individuals 
                      achieve their financial goals through smart investment decisions.
                    </p>
                    <div className="flex space-x-4">
                      <Button size="sm" variant="outline">
                        View Profile
                      </Button>
                      <Button size="sm" variant="outline">
                        More Articles
                      </Button>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* Comments Section */}
      <section className="py-12">
        <div className="container mx-auto px-6">
          <div className="max-w-4xl mx-auto">
            <h2 className="text-2xl font-bold mb-8">Leave a Comment</h2>
            
            <Card>
              <CardContent className="p-6">
                <form className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="name">Name</Label>
                      <Input id="name" placeholder="Your name" />
                    </div>
                    <div>
                      <Label htmlFor="email">Email</Label>
                      <Input id="email" type="email" placeholder="Your email" />
                    </div>
                  </div>
                  <div>
                    <Label htmlFor="comment">Comment</Label>
                    <Textarea 
                      id="comment" 
                      placeholder="Share your thoughts..."
                      rows={4}
                    />
                  </div>
                  <Button>Submit Comment</Button>
                </form>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* Related Articles */}
      <section className="py-12 bg-muted/30">
        <div className="container mx-auto px-6">
          <div className="max-w-6xl mx-auto">
            <h2 className="text-3xl font-bold mb-8">Related Articles</h2>
            
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              {relatedPosts.map((post) => (
                <Card key={post.id} className="hover:shadow-lg transition-shadow">
                  <CardContent className="p-6">
                    <div className="h-32 bg-gradient-to-r from-primary/20 to-primary/40 rounded-lg flex items-center justify-center mb-4">
                      <span className="text-2xl">📊</span>
                    </div>
                    <h3 className="font-semibold mb-2 line-clamp-2">{post.title}</h3>
                    <p className="text-muted-foreground text-sm mb-4 line-clamp-2">{post.excerpt}</p>
                    <div className="text-xs text-muted-foreground mb-4">{post.date}</div>
                    <Link to={`/blog/${post.id}`}>
                      <Button variant="outline" size="sm" className="w-full">
                        Read More
                      </Button>
                    </Link>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Newsletter Signup */}
      <section className="py-16 bg-primary text-white">
        <div className="container mx-auto px-6">
          <div className="max-w-4xl mx-auto text-center">
            <h2 className="text-3xl font-bold mb-6">Get More Investment Insights</h2>
            <p className="text-xl mb-8">
              Subscribe to our newsletter for weekly market updates and expert investment advice.
            </p>
            <div className="flex flex-col md:flex-row gap-4 max-w-lg mx-auto">
              <Input
                placeholder="Enter your email"
                className="flex-1 bg-white text-black"
              />
              <Button className="bg-white text-primary hover:bg-gray-100">
                Subscribe
              </Button>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
};

export default BlogDetail;
