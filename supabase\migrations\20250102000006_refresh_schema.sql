-- Refresh schema cache by recreating the users table with correct references

-- First, let's see what constraints exist
SELECT conname, conrelid::regclass, confrelid::regclass 
FROM pg_constraint 
WHERE conrelid = 'public.users'::regclass;

-- Drop and recreate the users table to clear any cached references
DROP TABLE IF EXISTS public.users CASCADE;

CREATE TABLE public.users (
  id uuid NOT NULL,
  username character varying UNIQUE,
  role_id uuid,
  mobile character varying UNIQUE,
  first_name character varying,
  last_name character varying,
  email character varying,
  is_active boolean DEFAULT true,
  is_deleted boolean DEFAULT false,
  created_at timestamp with time zone DEFAULT now(),
  updated_at timestamp with time zone DEFAULT now(),
  created_by uuid,
  updated_by uuid,
  CONSTRAINT users_pkey PRIMARY KEY (id),
  CONSTRAINT users_role_id_fkey FOREIGN KEY (role_id) REFERENCES public.roles(id)
);

-- Recreate indexes
CREATE INDEX idx_users_role_id ON public.users(role_id);
CREATE INDEX idx_users_mobile ON public.users(mobile);

-- Enable RLS
ALTER TABLE public.users ENABLE ROW LEVEL SECURITY;

-- Recreate RLS policy
CREATE POLICY "Allow authenticated users" ON public.users
FOR ALL USING (auth.uid() IS NOT NULL);

-- Recreate trigger
CREATE TRIGGER update_users_updated_at 
BEFORE UPDATE ON public.users 
FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();