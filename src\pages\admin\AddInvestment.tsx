import { useState, useEffect } from "react";
import { useNavigate, useParams } from "react-router-dom";
import { Card } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import { Badge } from "@/components/ui/badge";
import { ArrowLeft, Calculator, Users, TrendingUp } from "lucide-react";
import { toast } from "sonner";
import { supabase } from "@/integrations/supabase/client";

const AddInvestment = () => {
  const navigate = useNavigate();
  const { id } = useParams();
  const isEditing = !!id;
  const [loading, setLoading] = useState(false);
  const [clients, setClients] = useState<any[]>([]);
  const [schemes, setSchemes] = useState<any[]>([]);
  const [teamMembers, setTeamMembers] = useState<any[]>([]);
  const [selectedScheme, setSelectedScheme] = useState<any>(null);
  const [formData, setFormData] = useState({
    client_id: "",
    scheme_id: "",
    amount: "",
    start_date: new Date().toISOString().split('T')[0],
    team_members: [] as string[],
    remarks: ""
  });
  const [calculations, setCalculations] = useState({
    end_date: "",
    monthly_interest: 0,
    total_expected_return: 0,
    maturity_amount: 0,
    total_commission: 0,
    commission_per_member: 0
  });

  useEffect(() => {
    fetchClients();
    fetchSchemes();
    fetchTeamMembers();
    if (isEditing && id) {
      fetchInvestment();
    }
  }, [isEditing, id]);

  useEffect(() => {
    if (selectedScheme && formData.amount && formData.start_date) {
      calculateInvestmentDetails();
    }
  }, [selectedScheme, formData.amount, formData.start_date, formData.team_members]);

  const fetchClients = async () => {
    try {
      const { data, error } = await supabase
        .from('clients')
        .select('id, first_name, last_name, email')
        .eq('is_active', true)
        .eq('is_deleted', false);

      if (error) throw error;
      setClients(data || []);
    } catch (error: any) {
      toast.error('Failed to fetch clients: ' + error.message);
    }
  };

  const fetchSchemes = async () => {
    try {
      const { data, error } = await supabase
        .from('schemes')
        .select('*')
        .eq('is_active', true)
        .eq('is_deleted', false);

      if (error) throw error;
      setSchemes(data || []);
    } catch (error: any) {
      toast.error('Failed to fetch schemes: ' + error.message);
    }
  };

  const fetchTeamMembers = async () => {
    try {
      const { data, error } = await supabase
        .from('team_members')
        .select('id, first_name, last_name, refer_code, commission_pct')
        .eq('is_active', true)
        .eq('is_deleted', false);

      if (error) throw error;
      setTeamMembers(data || []);
    } catch (error: any) {
      toast.error('Failed to fetch team members: ' + error.message);
    }
  };

  const fetchInvestment = async () => {
    try {
      const { data, error } = await supabase
        .from('investments')
        .select('*')
        .eq('id', id)
        .single();

      if (error) throw error;

      setFormData({
        client_id: data.client_id,
        scheme_id: data.scheme_id,
        amount: data.amount.toString(),
        start_date: data.start_date,
        team_members: data.team_members ? JSON.parse(data.team_members).map((tm: any) => tm.id) : [],
        remarks: data.remarks || ''
      });
    } catch (error: any) {
      toast.error('Failed to fetch investment: ' + error.message);
    }
  };

  const handleSchemeChange = (schemeId: string) => {
    const scheme = schemes.find(s => s.id === schemeId);
    setSelectedScheme(scheme);
    setFormData({ ...formData, scheme_id: schemeId });
  };

  const handleTeamMemberToggle = (memberId: string) => {
    const updatedMembers = formData.team_members.includes(memberId)
      ? formData.team_members.filter(id => id !== memberId)
      : [...formData.team_members, memberId];

    setFormData({ ...formData, team_members: updatedMembers });
  };

  const calculateInvestmentDetails = () => {
    if (!selectedScheme || !formData.amount || !formData.start_date) return;

    const amount = parseFloat(formData.amount);
    const startDate = new Date(formData.start_date);
    const endDate = new Date(startDate);
    endDate.setMonth(endDate.getMonth() + selectedScheme.tenure_months);

    const monthlyInterest = (amount * selectedScheme.monthly_interest_pct) / 100;
    const totalExpectedReturn = monthlyInterest * selectedScheme.tenure_months;
    const maturityAmount = amount + totalExpectedReturn;

    // Calculate commission
    const schemeCommission = selectedScheme.commission_pct || 0;
    const totalCommission = (amount * schemeCommission) / 100;
    const commissionPerMember = formData.team_members.length > 0
      ? totalCommission / formData.team_members.length
      : 0;

    setCalculations({
      end_date: endDate.toISOString().split('T')[0],
      monthly_interest: monthlyInterest,
      total_expected_return: totalExpectedReturn,
      maturity_amount: maturityAmount,
      total_commission: totalCommission,
      commission_per_member: commissionPerMember
    });
  };

  const generateInvestmentCode = () => {
    const prefix = 'INV';
    const timestamp = Date.now().toString().slice(-6);
    const random = Math.random().toString(36).substring(2, 5).toUpperCase();
    return `${prefix}${timestamp}${random}`;
  };

  const createPaymentSchedule = async (investmentId: string) => {
    const payments = [];
    const startDate = new Date(formData.start_date);

    for (let month = 1; month <= selectedScheme.tenure_months; month++) {
      const dueDate = new Date(startDate);
      dueDate.setMonth(dueDate.getMonth() + month);

      payments.push({
        investment_id: investmentId,
        month_number: month,
        due_date: dueDate.toISOString().split('T')[0],
        amount: calculations.monthly_interest,
        status: 'pending'
      });
    }

    const { error } = await supabase
      .from('investment_payments')
      .insert(payments);

    if (error) throw error;
  };

  const createTransaction = async (investmentId: string) => {
    const transactionCode = `TXN${Date.now()}`;

    const { error } = await supabase
      .from('transactions')
      .insert({
        transaction_code: transactionCode,
        investment_id: investmentId,
        client_id: formData.client_id,
        transaction_type: 'investment_allocation',
        amount: parseFloat(formData.amount),
        description: `Investment in ${selectedScheme.name}`,
        reference_number: generateInvestmentCode(),
        payment_method: 'bank_transfer',
        status: 'completed'
      });

    if (error) throw error;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);

    try {
      if (!selectedScheme) {
        toast.error('Please select a scheme');
        return;
      }

      const amount = parseFloat(formData.amount);
      if (amount < selectedScheme.min_investment) {
        toast.error(`Minimum investment amount is ₹${selectedScheme.min_investment.toLocaleString()}`);
        return;
      }

      if (selectedScheme.max_investment && amount > selectedScheme.max_investment) {
        toast.error(`Maximum investment amount is ₹${selectedScheme.max_investment.toLocaleString()}`);
        return;
      }

      // Prepare team members data with commission split
      const teamMembersData = formData.team_members.map(memberId => {
        const member = teamMembers.find(tm => tm.id === memberId);
        return {
          id: memberId,
          name: `${member.first_name} ${member.last_name}`,
          refer_code: member.refer_code,
          commission_amount: calculations.commission_per_member
        };
      });

      const investmentData = {
        investment_code: generateInvestmentCode(),
        client_id: formData.client_id,
        scheme_id: formData.scheme_id,
        scheme_snapshot: JSON.stringify(selectedScheme),
        amount: amount,
        start_date: formData.start_date,
        end_date: calculations.end_date,
        monthly_interest: calculations.monthly_interest,
        total_expected_return: calculations.total_expected_return,
        maturity_amount: calculations.maturity_amount,
        team_members: JSON.stringify(teamMembersData),
        total_commission_pct: selectedScheme.commission_pct || 0,
        status: 'active'
      };

      if (isEditing) {
        const { error } = await supabase
          .from('investments')
          .update(investmentData)
          .eq('id', id);

        if (error) throw error;
        toast.success("Investment updated successfully!");
      } else {
        const { data: investment, error } = await supabase
          .from('investments')
          .insert(investmentData)
          .select()
          .single();

        if (error) throw error;

        // Create payment schedule
        await createPaymentSchedule(investment.id);

        // Create transaction record
        await createTransaction(investment.id);

        toast.success("Investment created successfully!");
      }

      navigate("/admin/investments");
    } catch (error: any) {
      toast.error(`Failed to ${isEditing ? 'update' : 'create'} investment: ` + error.message);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50 p-6">
      <div className=" mx-auto space-y-8">
        <header className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <Button
              variant="outline"
              size="sm"
              onClick={() => navigate("/admin/investments")}
              className="shadow-sm hover:shadow-md transition-shadow"
            >
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Investments
            </Button>
            <div>
              <h1 className="text-3xl font-bold bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent">
                {isEditing ? 'Edit Investment' : 'Add New Investment'}
              </h1>
              <p className="text-muted-foreground mt-1">
                {isEditing ? 'Update investment details' : 'Create a new investment with automatic calculations'}
              </p>
            </div>
          </div>
        </header>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Investment Form */}
          <div className="lg:col-span-2">
            <Card className="shadow-xl border-0 bg-white/95 backdrop-blur-sm">
              <div className="p-8">
                <form onSubmit={handleSubmit} className="space-y-8">
                  {/* Basic Details */}
                  <div className="space-y-6">
                    <div className="flex items-center space-x-3">
                      <div className="w-8 h-8 bg-gradient-to-r from-blue-500 to-indigo-500 rounded-lg flex items-center justify-center">
                        <span className="text-white font-semibold text-sm">1</span>
                      </div>
                      <h3 className="text-xl font-semibold text-gray-800">Investment Details</h3>
                    </div>

                    <div className="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-xl p-6">
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div className="space-y-2">
                          <Label htmlFor="client_id" className="text-sm font-medium text-gray-700">Client *</Label>
                          <Select value={formData.client_id} onValueChange={(value) => setFormData({ ...formData, client_id: value })}>
                            <SelectTrigger className="border-gray-300 focus:border-blue-500 focus:ring-blue-500/20">
                              <SelectValue placeholder="Select client" />
                            </SelectTrigger>
                            <SelectContent>
                              {clients.map((client) => (
                                <SelectItem key={client.id} value={client.id}>
                                  {client.first_name} {client.last_name} ({client.email})
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                        </div>
                        <div className="space-y-2">
                          <Label htmlFor="scheme_id" className="text-sm font-medium text-gray-700">Scheme *</Label>
                          <Select value={formData.scheme_id} onValueChange={handleSchemeChange}>
                            <SelectTrigger className="border-gray-300 focus:border-blue-500 focus:ring-blue-500/20">
                              <SelectValue placeholder="Select scheme" />
                            </SelectTrigger>
                            <SelectContent>
                              {schemes.map((scheme) => (
                                <SelectItem key={scheme.id} value={scheme.id}>
                                  {scheme.name} ({scheme.monthly_interest_pct}% monthly)
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                        </div>
                        <div className="space-y-2">
                          <Label htmlFor="amount" className="text-sm font-medium text-gray-700">Investment Amount (₹) *</Label>
                          <Input
                            id="amount"
                            type="number"
                            value={formData.amount}
                            onChange={(e) => setFormData({ ...formData, amount: e.target.value })}
                            placeholder="Enter investment amount"
                            className="border-gray-300 focus:border-blue-500 focus:ring-blue-500/20"
                            required
                          />
                          {selectedScheme && (
                            <p className="text-xs text-gray-500">
                              Min: ₹{selectedScheme.min_investment?.toLocaleString()}
                              {selectedScheme.max_investment && ` | Max: ₹${selectedScheme.max_investment?.toLocaleString()}`}
                            </p>
                          )}
                        </div>
                        <div className="space-y-2">
                          <Label htmlFor="start_date" className="text-sm font-medium text-gray-700">Start Date *</Label>
                          <Input
                            id="start_date"
                            type="date"
                            value={formData.start_date}
                            onChange={(e) => setFormData({ ...formData, start_date: e.target.value })}
                            className="border-gray-300 focus:border-blue-500 focus:ring-blue-500/20"
                            required
                          />
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Team Members Selection */}
                  <div className="space-y-6">
                    <div className="flex items-center space-x-3">
                      <div className="w-8 h-8 bg-gradient-to-r from-green-500 to-emerald-500 rounded-lg flex items-center justify-center">
                        <span className="text-white font-semibold text-sm">2</span>
                      </div>
                      <h3 className="text-xl font-semibold text-gray-800">Commission Distribution</h3>
                    </div>

                    <div className="bg-gradient-to-r from-green-50 to-emerald-50 rounded-xl p-6">
                      <div className="space-y-4">
                        <p className="text-sm text-gray-600">Select team members who will receive commission for this investment:</p>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          {teamMembers.map((member) => (
                            <div
                              key={member.id}
                              className={`p-4 rounded-lg border-2 cursor-pointer transition-all ${formData.team_members.includes(member.id)
                                  ? 'border-green-500 bg-green-50'
                                  : 'border-gray-200 hover:border-green-300'
                                }`}
                              onClick={() => handleTeamMemberToggle(member.id)}
                            >
                              <div className="flex items-center justify-between">
                                <div>
                                  <p className="font-medium">{member.first_name} {member.last_name}</p>
                                  <p className="text-sm text-gray-500">{member.refer_code}</p>
                                </div>
                                <Badge variant={formData.team_members.includes(member.id) ? 'default' : 'outline'}>
                                  {formData.team_members.includes(member.id) ? 'Selected' : 'Select'}
                                </Badge>
                              </div>
                            </div>
                          ))}
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Remarks */}
                  <div className="space-y-6">
                    <div className="flex items-center space-x-3">
                      <div className="w-8 h-8 bg-gradient-to-r from-purple-500 to-pink-500 rounded-lg flex items-center justify-center">
                        <span className="text-white font-semibold text-sm">3</span>
                      </div>
                      <h3 className="text-xl font-semibold text-gray-800">Additional Information</h3>
                    </div>

                    <div className="bg-gradient-to-r from-purple-50 to-pink-50 rounded-xl p-6">
                      <div className="space-y-2">
                        <Label htmlFor="remarks" className="text-sm font-medium text-gray-700">Remarks</Label>
                        <Textarea
                          id="remarks"
                          value={formData.remarks}
                          onChange={(e) => setFormData({ ...formData, remarks: e.target.value })}
                          placeholder="Enter any additional remarks or notes"
                          className="border-gray-300 focus:border-purple-500 focus:ring-purple-500/20"
                          rows={3}
                        />
                      </div>
                    </div>
                  </div>

                  <div className="flex justify-end space-x-4">
                    <Button type="button" variant="outline" onClick={() => navigate("/admin/investments")}>
                      Cancel
                    </Button>
                    <Button type="submit" disabled={loading || !selectedScheme}>
                      {loading ? (isEditing ? "Updating..." : "Creating...") : (isEditing ? "Update Investment" : "Create Investment")}
                    </Button>
                  </div>
                </form>
              </div>
            </Card>
          </div>

          {/* Calculations Panel */}
          <div className="space-y-6">
            {selectedScheme && formData.amount && (
              <>
                {/* Scheme Details */}
                <Card className="p-6 bg-gradient-to-br from-blue-50 to-indigo-100">
                  <h4 className="font-semibold text-lg mb-4 flex items-center">
                    <TrendingUp className="h-5 w-5 mr-2 text-blue-600" />
                    Scheme Details
                  </h4>
                  <div className="space-y-3">
                    <div className="flex justify-between">
                      <span className="text-sm text-gray-600">Scheme:</span>
                      <span className="font-medium">{selectedScheme.name}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm text-gray-600">Monthly Interest:</span>
                      <span className="font-medium text-green-600">{selectedScheme.monthly_interest_pct}%</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm text-gray-600">Tenure:</span>
                      <span className="font-medium">{selectedScheme.tenure_months} months</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm text-gray-600">Commission:</span>
                      <span className="font-medium">{selectedScheme.commission_pct || 0}%</span>
                    </div>
                  </div>
                </Card>

                {/* Investment Calculations */}
                <Card className="p-6 bg-gradient-to-br from-green-50 to-emerald-100">
                  <h4 className="font-semibold text-lg mb-4 flex items-center">
                    <Calculator className="h-5 w-5 mr-2 text-green-600" />
                    Investment Calculations
                  </h4>
                  <div className="space-y-3">
                    <div className="flex justify-between">
                      <span className="text-sm text-gray-600">Investment Amount:</span>
                      <span className="font-medium">₹{parseFloat(formData.amount || '0').toLocaleString()}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm text-gray-600">Monthly Interest:</span>
                      <span className="font-medium text-green-600">₹{calculations.monthly_interest.toLocaleString()}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm text-gray-600">Total Returns:</span>
                      <span className="font-medium">₹{calculations.total_expected_return.toLocaleString()}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm text-gray-600">Maturity Amount:</span>
                      <span className="font-bold text-blue-600">₹{calculations.maturity_amount.toLocaleString()}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm text-gray-600">End Date:</span>
                      <span className="font-medium">{calculations.end_date}</span>
                    </div>
                  </div>
                </Card>

                {/* Commission Details */}
                {formData.team_members.length > 0 && (
                  <Card className="p-6 bg-gradient-to-br from-purple-50 to-pink-100">
                    <h4 className="font-semibold text-lg mb-4 flex items-center">
                      <Users className="h-5 w-5 mr-2 text-purple-600" />
                      Commission Distribution
                    </h4>
                    <div className="space-y-3">
                      <div className="flex justify-between">
                        <span className="text-sm text-gray-600">Total Commission:</span>
                        <span className="font-medium">₹{calculations.total_commission.toLocaleString()}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-sm text-gray-600">Team Members:</span>
                        <span className="font-medium">{formData.team_members.length}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-sm text-gray-600">Per Member:</span>
                        <span className="font-bold text-purple-600">₹{calculations.commission_per_member.toLocaleString()}</span>
                      </div>
                    </div>
                  </Card>
                )}
              </>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default AddInvestment;