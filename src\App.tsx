
import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route } from "react-router-dom";
import { AuthProvider } from "@/contexts/AuthContext";

// Dashboard Layout
import AdminLayout from "@/components/layouts/AdminLayout";

// Admin Routes
import AdminDashboard from "@/pages/admin/AdminDashboard";
import ClientsManagement from "@/pages/admin/ClientsManagement";
import AddClient from "@/pages/admin/AddClient";
import ViewClient from "@/pages/admin/ViewClient";
import InvestmentManagement from "@/pages/admin/InvestmentManagement";
import SchemesManagement from "@/pages/admin/SchemesManagement";
import AddScheme from "@/pages/admin/AddScheme";
import ViewScheme from "@/pages/admin/ViewScheme";
import AddInvestment from "@/pages/admin/AddInvestment";
import ViewInvestment from "@/pages/admin/ViewInvestment";
import BrokerInvestmentManagement from "@/pages/admin/BrokerInvestmentManagement";
import BrokersManagement from "@/pages/admin/BrokersManagement";
import TeamManagement from "@/pages/admin/TeamManagement";
import AddTeamMember from "@/pages/admin/AddTeamMember";
import ViewTeamMember from "@/pages/admin/ViewTeamMember";
import Calculator from "@/pages/admin/Calculator";
import Portfolio from "@/pages/admin/Portfolio";
import BlogManagement from "@/pages/admin/BlogManagement";
import AlertsNotifications from "@/pages/admin/AlertsNotifications";
import RolesManagement from "@/pages/admin/RolesManagement";
import PermissionsManagement from "@/pages/admin/PermissionsManagement";
import AdminSettings from "@/pages/admin/AdminSettings";

// Team Routes
import TeamDashboard from "@/pages/team/TeamDashboard";
import TeamClients from "@/pages/team/TeamClients";
import TeamInvestments from "@/pages/team/TeamInvestments";
import TeamCalculator from "@/pages/team/TeamCalculator";
import TeamPortfolio from "@/pages/team/TeamPortfolio";
import TeamSettings from "@/pages/team/TeamSettings";

// Client Routes
import ClientDashboard from "@/pages/client/ClientDashboard";
import ClientInvestments from "@/pages/client/ClientInvestments";
import ClientPortfolio from "@/pages/client/ClientPortfolio";
import ClientSettings from "@/pages/client/ClientSettings";

// Marketing Website Routes
import MarketingLayout from "@/components/layouts/MarketingLayout";
import HomePage from "@/pages/marketing/HomePage";
import StockTraining from "@/pages/marketing/StockTraining";
import Charity from "@/pages/marketing/Charity";
import AboutUs from "@/pages/marketing/AboutUs";
import Contact from "@/pages/marketing/Contact";
import FAQs from "@/pages/marketing/FAQs";
import Blogs from "@/pages/marketing/Blogs";
import BlogDetail from "@/pages/marketing/BlogDetail";

// Auth Pages
import LoginPage from "@/pages/auth/LoginPage";
import ForgotPasswordPage from "@/pages/auth/ForgotPasswordPage";
import ResetPasswordPage from "@/pages/auth/ResetPasswordPage";
import AuthCallback from "@/pages/auth/AuthCallback";

const queryClient = new QueryClient();

const App = () => (
  <QueryClientProvider client={queryClient}>
    <TooltipProvider>
      <AuthProvider>
        <Toaster />
        <Sonner />
        <BrowserRouter>
          <Routes>
            {/* Auth Routes */}
            <Route path="/login" element={<LoginPage />} />
            <Route path="/forgot-password" element={<ForgotPasswordPage />} />
            <Route path="/reset-password" element={<ResetPasswordPage />} />
            <Route path="/auth/callback" element={<AuthCallback />} />
            
            {/* Dashboard Routes */}
            <Route path="/admin" element={<AdminLayout />}>
              <Route index element={<AdminDashboard />} />
              <Route path="clients" element={<ClientsManagement />} />
              <Route path="clients/add" element={<AddClient />} />
              <Route path="clients/view/:id" element={<ViewClient />} />
              <Route path="clients/edit/:id" element={<AddClient />} />
              <Route path="investments" element={<InvestmentManagement />} />
              <Route path="schemes" element={<SchemesManagement />} />
              <Route path="schemes/add" element={<AddScheme />} />
              <Route path="schemes/edit/:id" element={<AddScheme />} />
              <Route path="schemes/view/:id" element={<ViewScheme />} />
              <Route path="investments/add" element={<AddInvestment />} />
              <Route path="investments/edit/:id" element={<AddInvestment />} />
              <Route path="investments/view/:id" element={<ViewInvestment />} />
              <Route path="broker-investments" element={<BrokerInvestmentManagement />} />
              <Route path="brokers" element={<BrokersManagement />} />
              <Route path="team" element={<TeamManagement />} />
              <Route path="team/add" element={<AddTeamMember />} />
              <Route path="team/view/:id" element={<ViewTeamMember />} />
              <Route path="team/edit/:id" element={<AddTeamMember />} />
              <Route path="calculator" element={<Calculator />} />
              <Route path="portfolio" element={<Portfolio />} />
              <Route path="blogs" element={<BlogManagement />} />
              <Route path="alerts" element={<AlertsNotifications />} />
              <Route path="roles" element={<RolesManagement />} />
              <Route path="permissions" element={<PermissionsManagement />} />
              <Route path="settings" element={<AdminSettings />} />
            </Route>

            <Route path="/team" element={<AdminLayout />}>
              <Route index element={<TeamDashboard />} />
              <Route path="clients" element={<TeamClients />} />
              <Route path="investments" element={<TeamInvestments />} />
              <Route path="calculator" element={<TeamCalculator />} />
              <Route path="portfolio" element={<TeamPortfolio />} />
              <Route path="settings" element={<TeamSettings />} />
            </Route>

            <Route path="/client" element={<AdminLayout />}>
              <Route index element={<ClientDashboard />} />
              <Route path="investments" element={<ClientInvestments />} />
              <Route path="portfolio" element={<ClientPortfolio />} />
              <Route path="settings" element={<ClientSettings />} />
            </Route>

            {/* Marketing Website Routes */}
            <Route path="/" element={<MarketingLayout />}>
              <Route index element={<HomePage />} />
              <Route path="stock-training" element={<StockTraining />} />
              <Route path="charity" element={<Charity />} />
              <Route path="about" element={<AboutUs />} />
              <Route path="contact" element={<Contact />} />
              <Route path="faqs" element={<FAQs />} />
              <Route path="blogs" element={<Blogs />} />
              <Route path="blog/:slug" element={<BlogDetail />} />
            </Route>
          </Routes>
        </BrowserRouter>
      </AuthProvider>
    </TooltipProvider>
  </QueryClientProvider>
);

export default App;
