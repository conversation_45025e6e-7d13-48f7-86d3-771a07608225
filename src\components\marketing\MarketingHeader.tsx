
import { <PERSON> } from "react-router-dom";
import { But<PERSON> } from "@/components/ui/button";
import { Menu, Phone, Mail } from "lucide-react";
import { useAuth } from "@/contexts/AuthContext";

const MarketingHeader = () => {
  const { isAuthenticated, user } = useAuth();

  const getDashboardPath = () => {
    if (!user) return '/login';
    return `/${user.role}`;
  };
  return (
    <header className="bg-white shadow-sm border-b">
      <div className="container mx-auto px-4">
        <div className="flex items-center justify-between h-16">
          <Link to="/" className="flex items-center space-x-2">
            <div className="h-10 w-10 bg-primary rounded-lg flex items-center justify-center">
              <span className="text-white font-bold text-xl">C</span>
            </div>
            <span className="text-2xl font-bold text-primary">Care Capital</span>
          </Link>

          <nav className="hidden md:flex items-center space-x-8">
            <Link to="/" className="text-gray-600 hover:text-primary transition-colors">
              Home
            </Link>
            <Link to="/stock-training" className="text-gray-600 hover:text-primary transition-colors">
              Stock Training
            </Link>
            <Link to="/charity" className="text-gray-600 hover:text-primary transition-colors">
              Charity
            </Link>
            <Link to="/about" className="text-gray-600 hover:text-primary transition-colors">
              About Us
            </Link>
            <Link to="/blogs" className="text-gray-600 hover:text-primary transition-colors">
              Blogs
            </Link>
            <Link to="/faqs" className="text-gray-600 hover:text-primary transition-colors">
              FAQs
            </Link>
            <Link to="/contact" className="text-gray-600 hover:text-primary transition-colors">
              Contact
            </Link>
          </nav>

          <div className="flex items-center space-x-4">
            {isAuthenticated ? (
              <Link to={getDashboardPath()}>
                <Button>Go to Dashboard</Button>
              </Link>
            ) : (
              <Link to="/login">
                <Button variant="outline">Login</Button>
              </Link>
            )}
            <Button className="md:hidden">
              <Menu className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </div>
    </header>
  );
};

export default MarketingHeader;
