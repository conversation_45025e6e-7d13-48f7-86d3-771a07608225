import { useState, useEffect } from 'react';

export type UserRole = 'admin' | 'team_member';

export interface Permission {
  canViewAnalytics: boolean;
  canManageUsers: boolean;
  canEditSettings: boolean;
  canViewReports: boolean;
  canManageProjects: boolean;
  canDeleteData: boolean;
  // Client permissions
  canViewClients: boolean;
  canAddClients: boolean;
  canEditClients: boolean;
  canDeleteClients: boolean;
  canExportClients: boolean;
}

const rolePermissions: Record<UserRole, Permission> = {
  admin: {
    canViewAnalytics: true,
    canManageUsers: true,
    canEditSettings: true,
    canViewReports: true,
    canManageProjects: true,
    canDeleteData: true,
    canViewClients: true,
    canAddClients: true,
    canEditClients: true,
    canDeleteClients: true,
    canExportClients: true,
  },
  team_member: {
    canViewAnalytics: true,
    canManageUsers: false,
    canEditSettings: false,
    canViewReports: true,
    canManageProjects: true,
    canDeleteData: false,
    canViewClients: true,
    canAddClients: false,
    canEditClients: false,
    canDeleteClients: false,
    canExportClients: false,
  },
};

export const usePermission = () => {
  const [userRole, setUserRole] = useState<UserRole>('team_member');
  const [permissions, setPermissions] = useState<Permission>(rolePermissions.team_member);

  useEffect(() => {
    // Get user role from localStorage or API
    const storedRole = localStorage.getItem('userRole') as UserRole;
    if (storedRole && rolePermissions[storedRole]) {
      setUserRole(storedRole);
      setPermissions(rolePermissions[storedRole]);
    }
  }, []);

  const hasPermission = (permission: keyof Permission): boolean => {
    return permissions[permission];
  };

  const updateUserRole = (role: UserRole) => {
    setUserRole(role);
    setPermissions(rolePermissions[role]);
    localStorage.setItem('userRole', role);
  };

  return {
    userRole,
    permissions,
    hasPermission,
    updateUserRole,
    isAdmin: userRole === 'admin',
    isTeamMember: userRole === 'team_member',
  };
};