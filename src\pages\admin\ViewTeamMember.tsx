import { useState, useEffect } from "react";
import { usePara<PERSON>, useNavigate } from "react-router-dom";
import { Card } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { ArrowLeft, Edit, Mail, Phone, MapPin, CreditCard, Building2, Users, Percent } from "lucide-react";
import { toast } from "sonner";
import { supabase } from "@/integrations/supabase/client";

const ViewTeamMember = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const [teamMember, setTeamMember] = useState<any>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    if (id) {
      fetchTeamMember();
    }
  }, [id]);

  const fetchTeamMember = async () => {
    try {
      const { data, error } = await supabase
        .from('team_members')
        .select(`
          *,
          clients!clients_referred_by_fkey(id, first_name, last_name, created_at)
        `)
        .eq('id', id)
        .single();

      if (error) throw error;

      const memberWithStats = {
        ...data,
        name: `${data.first_name} ${data.last_name || ''}`.trim(),
        totalClients: data.clients?.length || 0,
        status: data.is_active ? 'Active' : 'Inactive',
        joinDate: new Date(data.created_at).toLocaleDateString()
      };

      setTeamMember(memberWithStats);
    } catch (error: any) {
      toast.error('Failed to fetch team member: ' + error.message);
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-lg">Loading team member...</div>
      </div>
    );
  }

  if (!teamMember) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-lg">Team member not found</div>
      </div>
    );
  }

  return (
    <div className="space-y-8">
      <header className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Button variant="outline" size="sm" onClick={() => navigate("/admin/team")}>
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Team
          </Button>
          <div>
            <h1 className="text-4xl font-bold text-primary">{teamMember.name}</h1>
            <p className="text-muted-foreground">Team Member Details</p>
          </div>
        </div>
        <Button onClick={() => navigate(`/admin/team/edit/${teamMember.id}`)}>
          <Edit className="h-4 w-4 mr-2" />
          Edit Team Member
        </Button>
      </header>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        {/* Personal Information */}
        <Card className="p-6">
          <h3 className="text-lg font-semibold mb-4">Personal Information</h3>
          <div className="space-y-4">
            <div className="flex items-center space-x-3">
              <div className="h-12 w-12 rounded-full bg-primary text-white flex items-center justify-center">
                {teamMember.name.charAt(0)}
              </div>
              <div>
                <p className="font-medium">{teamMember.name}</p>
                <p className="text-sm text-muted-foreground">{teamMember.refer_code || 'No code'}</p>
              </div>
            </div>
            
            <div className="space-y-2">
              <div className="flex items-center space-x-2">
                <Mail className="h-4 w-4 text-muted-foreground" />
                <span className="text-sm">{teamMember.email || 'No email'}</span>
              </div>
              <div className="flex items-center space-x-2">
                <Phone className="h-4 w-4 text-muted-foreground" />
                <span className="text-sm">{teamMember.phone}</span>
              </div>
              <div className="flex items-center space-x-2">
                <MapPin className="h-4 w-4 text-muted-foreground" />
                <span className="text-sm">{teamMember.address || 'No address'}</span>
              </div>
            </div>

            <div className="pt-4 border-t">
              <p className="text-sm"><strong>City:</strong> {teamMember.city || 'Not provided'}</p>
              <p className="text-sm"><strong>State:</strong> {teamMember.state || 'Not provided'}</p>
              <p className="text-sm"><strong>Pincode:</strong> {teamMember.pincode || 'Not provided'}</p>
              <p className="text-sm"><strong>Country:</strong> {teamMember.country}</p>
            </div>
          </div>
        </Card>

        {/* Performance Summary */}
        <Card className="p-6">
          <h3 className="text-lg font-semibold mb-4">Performance Summary</h3>
          <div className="space-y-4">
            <div className="text-center p-4 bg-primary/10 rounded-lg">
              <p className="text-2xl font-bold text-primary">{teamMember.totalClients}</p>
              <p className="text-sm text-muted-foreground">Total Clients</p>
            </div>
            
            <div className="grid grid-cols-2 gap-4">
              <div className="text-center p-3 bg-gray-50 rounded-lg">
                <div className="flex items-center justify-center space-x-1">
                  <Percent className="h-4 w-4" />
                  <p className="text-xl font-semibold">{teamMember.commission_pct}</p>
                </div>
                <p className="text-xs text-muted-foreground">Commission %</p>
              </div>
              <div className="text-center p-3 bg-gray-50 rounded-lg">
                <Badge variant={teamMember.status === 'Active' ? 'default' : 'secondary'}>
                  {teamMember.status}
                </Badge>
                <p className="text-xs text-muted-foreground mt-1">Status</p>
              </div>
            </div>

            <div className="pt-4 border-t">
              <p className="text-sm"><strong>Join Date:</strong> {teamMember.joinDate}</p>
            </div>
          </div>
        </Card>

        {/* Bank Information */}
        <Card className="p-6">
          <h3 className="text-lg font-semibold mb-4">Bank Information</h3>
          <div className="space-y-4">
            <div className="flex items-center space-x-2">
              <Building2 className="h-4 w-4 text-muted-foreground" />
              <span className="text-sm">{teamMember.bank_name || 'Not provided'}</span>
            </div>
            <div className="flex items-center space-x-2">
              <CreditCard className="h-4 w-4 text-muted-foreground" />
              <span className="text-sm">{teamMember.account_number || 'Not provided'}</span>
            </div>
            
            <div className="space-y-2 pt-4 border-t">
              <p className="text-sm"><strong>IFSC Code:</strong> {teamMember.ifsc_code || 'Not provided'}</p>
              <p className="text-sm"><strong>Branch:</strong> {teamMember.branch_name || 'Not provided'}</p>
            </div>
          </div>
        </Card>
      </div>

      {/* Referred Clients */}
      {teamMember.clients?.length > 0 && (
        <Card className="p-6">
          <h3 className="text-lg font-semibold mb-4 flex items-center space-x-2">
            <Users className="h-5 w-5" />
            <span>Referred Clients</span>
          </h3>
          <div className="space-y-2">
            {teamMember.clients.slice(0, 10).map((client: any, index: number) => (
              <div key={index} className="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
                <div>
                  <p className="font-medium">{client.first_name} {client.last_name}</p>
                  <p className="text-sm text-muted-foreground">
                    Joined: {new Date(client.created_at).toLocaleDateString()}
                  </p>
                </div>
                <Button 
                  variant="outline" 
                  size="sm"
                  onClick={() => navigate(`/admin/clients/view/${client.id}`)}
                >
                  View
                </Button>
              </div>
            ))}
            {teamMember.clients.length > 10 && (
              <p className="text-sm text-muted-foreground text-center pt-2">
                And {teamMember.clients.length - 10} more clients...
              </p>
            )}
          </div>
        </Card>
      )}
    </div>
  );
};

export default ViewTeamMember;