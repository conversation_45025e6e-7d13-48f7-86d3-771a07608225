-- Fix RLS policies for investments and related tables

-- Add RLS policies for investments table
CREATE POLICY "Allow authenticated users" ON public.investments
FOR ALL USING (auth.uid() IS NOT NULL);

-- Add RLS policies for investment_payments table
CREATE POLICY "Allow authenticated users" ON public.investment_payments
FOR ALL USING (auth.uid() IS NOT NULL);

-- Add RLS policies for transactions table
CREATE POLICY "Allow authenticated users" ON public.transactions
FOR ALL USING (auth.uid() IS NOT NULL);