
import { Card } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Users, TrendingUp, Banknote, Building2, Bell, DollarSign } from "lucide-react";
import { LineChart, Line, ResponsiveContainer, XAxis, YAxis, Tooltip, Bar<PERSON>hart, Bar, Pie<PERSON>hart, Pie, Cell } from "recharts";

const investmentData = [
  { name: "<PERSON>", value: 1200000 },
  { name: "Feb", value: 1450000 },
  { name: "<PERSON>", value: 1800000 },
  { name: "Apr", value: 1650000 },
  { name: "May", value: 2100000 },
  { name: "<PERSON>", value: 2350000 },
];

const schemeData = [
  { name: "Fixed Deposit", value: 45, color: "#8884d8" },
  { name: "Mutual Fund", value: 30, color: "#82ca9d" },
  { name: "<PERSON><PERSON>", value: 15, color: "#ffc658" },
  { name: "Others", value: 10, color: "#ff7300" },
];

const AdminDashboard = () => {
  return (
    <div className="space-y-6">
      <header className="flex justify-between items-center">
        <div>
          <h1 className="text-4xl font-bold text-primary">Admin Dashboard</h1>
          <p className="text-muted-foreground">Welcome back! Here's your investment overview</p>
        </div>
        <div className="flex items-center space-x-4">
          <Button variant="outline">
            <Bell className="h-4 w-4 mr-2" />
            Notifications (3)
          </Button>
          <Button>Quick Actions</Button>
        </div>
      </header>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card className="p-6">
          <div className="flex items-start justify-between">
            <div>
              <p className="text-sm text-muted-foreground">Total Clients</p>
              <h2 className="text-3xl font-bold">1,247</h2>
              <p className="text-green-600 text-sm">+12% from last month</p>
            </div>
            <div className="p-3 bg-blue-100 rounded-full">
              <Users className="h-6 w-6 text-blue-600" />
            </div>
          </div>
        </Card>

        <Card className="p-6">
          <div className="flex items-start justify-between">
            <div>
              <p className="text-sm text-muted-foreground">Total Investment</p>
              <h2 className="text-3xl font-bold">₹2.35Cr</h2>
              <p className="text-green-600 text-sm">+8.5% from last month</p>
            </div>
            <div className="p-3 bg-green-100 rounded-full">
              <DollarSign className="h-6 w-6 text-green-600" />
            </div>
          </div>
        </Card>

        <Card className="p-6">
          <div className="flex items-start justify-between">
            <div>
              <p className="text-sm text-muted-foreground">Active Schemes</p>
              <h2 className="text-3xl font-bold">24</h2>
              <p className="text-blue-600 text-sm">3 new this month</p>
            </div>
            <div className="p-3 bg-purple-100 rounded-full">
              <Banknote className="h-6 w-6 text-purple-600" />
            </div>
          </div>
        </Card>

        <Card className="p-6">
          <div className="flex items-start justify-between">
            <div>
              <p className="text-sm text-muted-foreground">Broker Investment</p>
              <h2 className="text-3xl font-bold">₹45.2L</h2>
              <p className="text-orange-600 text-sm">+15.2% growth</p>
            </div>
            <div className="p-3 bg-orange-100 rounded-full">
              <Building2 className="h-6 w-6 text-orange-600" />
            </div>
          </div>
        </Card>
      </div>

      {/* Charts Section */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card className="p-6">
          <h3 className="text-lg font-semibold mb-4">Investment Trends</h3>
          <div className="h-[300px] w-full">
            <ResponsiveContainer width="100%" height="100%">
              <LineChart data={investmentData}>
                <XAxis dataKey="name" stroke="#888888" />
                <YAxis stroke="#888888" />
                <Tooltip formatter={(value) => [`₹${(value as number / 100000).toFixed(1)}L`, 'Investment']} />
                <Line
                  type="monotone"
                  dataKey="value"
                  stroke="#8884d8"
                  strokeWidth={3}
                  dot={{ fill: '#8884d8', strokeWidth: 2, r: 6 }}
                />
              </LineChart>
            </ResponsiveContainer>
          </div>
        </Card>

        <Card className="p-6">
          <h3 className="text-lg font-semibold mb-4">Scheme Distribution</h3>
          <div className="h-[300px] w-full">
            <ResponsiveContainer width="100%" height="100%">
              <PieChart>
                <Pie
                  data={schemeData}
                  cx="50%"
                  cy="50%"
                  outerRadius={80}
                  dataKey="value"
                  label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                >
                  {schemeData.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={entry.color} />
                  ))}
                </Pie>
                <Tooltip />
              </PieChart>
            </ResponsiveContainer>
          </div>
        </Card>
      </div>

      {/* Recent Activity & Maturity Alerts */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card className="p-6">
          <h3 className="text-lg font-semibold mb-4">Recent Transactions</h3>
          <div className="space-y-4">
            {[
              { name: "John Doe", amount: "₹2,50,000", scheme: "Fixed Deposit", time: "2 hours ago", type: "investment" },
              { name: "Sarah Wilson", amount: "₹75,000", scheme: "Mutual Fund", time: "4 hours ago", type: "withdrawal" },
              { name: "Mike Johnson", amount: "₹1,20,000", scheme: "Bonds", time: "6 hours ago", type: "investment" },
              { name: "Lisa Brown", amount: "₹3,00,000", scheme: "Fixed Deposit", time: "1 day ago", type: "investment" },
            ].map((transaction, i) => (
              <div key={i} className="flex items-center justify-between p-3 hover:bg-gray-50 rounded-lg">
                <div className="flex items-center space-x-3">
                  <div className={`p-2 rounded-full ${transaction.type === 'investment' ? 'bg-green-100' : 'bg-red-100'}`}>
                    <TrendingUp className={`h-4 w-4 ${transaction.type === 'investment' ? 'text-green-600' : 'text-red-600'}`} />
                  </div>
                  <div>
                    <p className="font-medium">{transaction.name}</p>
                    <p className="text-sm text-muted-foreground">{transaction.scheme}</p>
                  </div>
                </div>
                <div className="text-right">
                  <p className={`font-medium ${transaction.type === 'investment' ? 'text-green-600' : 'text-red-600'}`}>
                    {transaction.type === 'investment' ? '+' : '-'}{transaction.amount}
                  </p>
                  <p className="text-sm text-muted-foreground">{transaction.time}</p>
                </div>
              </div>
            ))}
          </div>
        </Card>

        <Card className="p-6">
          <h3 className="text-lg font-semibold mb-4">Maturity Alerts</h3>
          <div className="space-y-4">
            {[
              { name: "Alice Cooper", amount: "₹5,50,000", scheme: "Fixed Deposit", maturityDate: "2024-01-15", daysLeft: 3 },
              { name: "Bob Smith", amount: "₹2,25,000", scheme: "Bonds", maturityDate: "2024-01-18", daysLeft: 6 },
              { name: "Carol White", amount: "₹3,75,000", scheme: "Fixed Deposit", maturityDate: "2024-01-22", daysLeft: 10 },
              { name: "David Brown", amount: "₹1,80,000", scheme: "Mutual Fund", maturityDate: "2024-01-25", daysLeft: 13 },
            ].map((alert, i) => (
              <div key={i} className="flex items-center justify-between p-3 border border-orange-200 bg-orange-50 rounded-lg">
                <div className="flex items-center space-x-3">
                  <div className="p-2 bg-orange-100 rounded-full">
                    <Bell className="h-4 w-4 text-orange-600" />
                  </div>
                  <div>
                    <p className="font-medium">{alert.name}</p>
                    <p className="text-sm text-muted-foreground">{alert.scheme} - {alert.amount}</p>
                  </div>
                </div>
                <div className="text-right">
                  <p className="font-medium text-orange-600">{alert.daysLeft} days left</p>
                  <p className="text-sm text-muted-foreground">{alert.maturityDate}</p>
                </div>
              </div>
            ))}
          </div>
          <Button className="w-full mt-4" variant="outline">
            View All Alerts
          </Button>
        </Card>
      </div>
    </div>
  );
};

export default AdminDashboard;
