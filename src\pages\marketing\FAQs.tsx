
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from "@/components/ui/accordion";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Search, HelpCircle, Phone, Mail } from "lucide-react";
import { useState } from "react";

const FAQs = () => {
  const [searchTerm, setSearchTerm] = useState("");

  const faqCategories = [
    {
      category: "Getting Started",
      questions: [
        {
          question: "How do I start investing with Care Capital?",
          answer: "Getting started is simple! First, create an account on our platform, complete the KYC verification process, choose an investment plan that suits your goals, and make your first investment. Our team will guide you through each step."
        },
        {
          question: "What is the minimum investment amount?",
          answer: "Our minimum investment varies by scheme: Silver Plan starts at ₹25,000, Gold Plan at ₹50,000, and Platinum Plan at ₹1,00,000. This makes our investment opportunities accessible to a wide range of investors."
        },
        {
          question: "Do I need any prior investment experience?",
          answer: "No prior experience is required! We provide comprehensive guidance and educational resources to help you understand your investments. Our team of experts is always available to answer your questions."
        }
      ]
    },
    {
      category: "Investment Plans",
      questions: [
        {
          question: "What are the different investment schemes available?",
          answer: "We offer three main schemes: Silver Plan (10% annual returns, 6-18 months tenure), Gold Plan (12% annual returns, 12-24 months tenure), and Platinum Plan (15% annual returns, 24-36 months tenure)."
        },
        {
          question: "How are the returns calculated and paid?",
          answer: "Returns are calculated on an annual basis and can be paid monthly, quarterly, or at maturity based on your chosen plan. All payments are made directly to your registered bank account."
        },
        {
          question: "Can I withdraw my investment before maturity?",
          answer: "Early withdrawal options vary by scheme. Some plans allow partial withdrawal after a lock-in period with minimal charges, while others are fixed until maturity. Please check your specific plan terms."
        }
      ]
    },
    {
      category: "Security & Safety",
      questions: [
        {
          question: "How safe are my investments with Care Capital?",
          answer: "Your investments are secured through multiple layers of protection including regulatory compliance, segregated accounts, insurance coverage, and regular audits by independent agencies."
        },
        {
          question: "Is Care Capital regulated by financial authorities?",
          answer: "Yes, we are registered and regulated by appropriate financial authorities. We maintain full compliance with all regulatory requirements and undergo regular inspections."
        },
        {
          question: "What happens to my money if Care Capital faces financial difficulties?",
          answer: "Client funds are held in segregated accounts separate from company assets. Additionally, we maintain insurance coverage and have established procedures to protect investor interests under all circumstances."
        }
      ]
    },
    {
      category: "Account Management",
      questions: [
        {
          question: "How can I track my investments?",
          answer: "You can track all your investments through our online portal or mobile app. You'll have access to real-time portfolio updates, transaction history, and performance reports 24/7."
        },
        {
          question: "How do I update my personal information?",
          answer: "You can update most personal information through your online account. For certain changes like bank account details, you may need to submit additional documentation for verification."
        },
        {
          question: "What if I forget my login credentials?",
          answer: "Use the 'Forgot Password' option on our login page. For other issues, contact our customer support team who can help you regain access to your account securely."
        }
      ]
    },
    {
      category: "Payments & Transactions",
      questions: [
        {
          question: "What payment methods do you accept?",
          answer: "We accept bank transfers, UPI payments, NEFT/RTGS, and cheques. Online payment options are available for quick and convenient transactions."
        },
        {
          question: "How long does it take for payments to be processed?",
          answer: "Investment deposits are typically processed within 24-48 hours. Return payments are made on schedule according to your plan terms, usually within 1-2 business days."
        },
        {
          question: "Are there any hidden charges or fees?",
          answer: "We believe in complete transparency. All fees and charges are clearly mentioned in your investment agreement. There are no hidden charges - what you see is what you pay."
        }
      ]
    }
  ];

  const filteredFAQs = faqCategories.map(category => ({
    ...category,
    questions: category.questions.filter(
      q => q.question.toLowerCase().includes(searchTerm.toLowerCase()) ||
           q.answer.toLowerCase().includes(searchTerm.toLowerCase())
    )
  })).filter(category => category.questions.length > 0);

  return (
    <div className="min-h-screen">
      {/* Hero Section */}
      <section className="bg-gradient-to-r from-primary to-primary/80 text-white py-20">
        <div className="container mx-auto px-6 text-center">
          <h1 className="text-4xl md:text-6xl font-bold mb-6">
            Frequently Asked Questions
          </h1>
          <p className="text-xl mb-8 max-w-3xl mx-auto">
            Find answers to common questions about our investment plans, processes, and services.
          </p>
        </div>
      </section>

      {/* Search Section */}
      <section className="py-12">
        <div className="container mx-auto px-6">
          <div className="max-w-2xl mx-auto">
            <div className="relative">
              <Search className="absolute left-3 top-3 h-5 w-5 text-muted-foreground" />
              <Input
                className="pl-10 py-3 text-lg"
                placeholder="Search for answers..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </div>
          </div>
        </div>
      </section>

      {/* FAQ Content */}
      <section className="py-8">
        <div className="container mx-auto px-6">
          <div className="max-w-4xl mx-auto space-y-8">
            {filteredFAQs.length > 0 ? (
              filteredFAQs.map((category, categoryIndex) => (
                <Card key={categoryIndex}>
                  <CardHeader>
                    <CardTitle className="flex items-center">
                      <HelpCircle className="mr-2 h-5 w-5 text-primary" />
                      {category.category}
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <Accordion type="single" collapsible className="w-full">
                      {category.questions.map((faq, index) => (
                        <AccordionItem key={index} value={`item-${categoryIndex}-${index}`}>
                          <AccordionTrigger className="text-left">
                            {faq.question}
                          </AccordionTrigger>
                          <AccordionContent className="text-muted-foreground">
                            {faq.answer}
                          </AccordionContent>
                        </AccordionItem>
                      ))}
                    </Accordion>
                  </CardContent>
                </Card>
              ))
            ) : (
              <Card>
                <CardContent className="text-center py-12">
                  <HelpCircle className="h-12 w-12 mx-auto mb-4 text-muted-foreground" />
                  <h3 className="text-xl font-semibold mb-2">No results found</h3>
                  <p className="text-muted-foreground mb-4">
                    We couldn't find any FAQs matching your search. Try different keywords or browse all categories.
                  </p>
                  <Button variant="outline" onClick={() => setSearchTerm("")}>
                    Clear Search
                  </Button>
                </CardContent>
              </Card>
            )}
          </div>
        </div>
      </section>

      {/* Contact Support */}
      <section className="py-16 bg-muted/30">
        <div className="container mx-auto px-6">
          <div className="text-center mb-8">
            <h2 className="text-3xl font-bold mb-4">Still Have Questions?</h2>
            <p className="text-lg text-muted-foreground">
              Can't find what you're looking for? Our support team is here to help.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 max-w-2xl mx-auto">
            <Card>
              <CardContent className="text-center pt-6">
                <Phone className="h-8 w-8 mx-auto mb-4 text-primary" />
                <h3 className="font-semibold mb-2">Call Us</h3>
                <p className="text-muted-foreground mb-4">Speak directly with our experts</p>
                <Button className="w-full">
                  +91 22 1234 5678
                </Button>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="text-center pt-6">
                <Mail className="h-8 w-8 mx-auto mb-4 text-primary" />
                <h3 className="font-semibold mb-2">Email Us</h3>
                <p className="text-muted-foreground mb-4">Get detailed written responses</p>
                <Button className="w-full" variant="outline">
                  <EMAIL>
                </Button>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* Popular Topics */}
      <section className="py-16">
        <div className="container mx-auto px-6">
          <h2 className="text-3xl font-bold text-center mb-12">Popular Topics</h2>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 max-w-4xl mx-auto">
            <Button variant="outline" className="h-auto p-4 text-left justify-start">
              <div>
                <div className="font-semibold">Investment Plans</div>
                <div className="text-sm text-muted-foreground">Compare our schemes</div>
              </div>
            </Button>
            
            <Button variant="outline" className="h-auto p-4 text-left justify-start">
              <div>
                <div className="font-semibold">Account Setup</div>
                <div className="text-sm text-muted-foreground">Getting started guide</div>
              </div>
            </Button>
            
            <Button variant="outline" className="h-auto p-4 text-left justify-start">
              <div>
                <div className="font-semibold">Payment Methods</div>
                <div className="text-sm text-muted-foreground">How to invest</div>
              </div>
            </Button>
            
            <Button variant="outline" className="h-auto p-4 text-left justify-start">
              <div>
                <div className="font-semibold">Returns & Payouts</div>
                <div className="text-sm text-muted-foreground">How you earn</div>
              </div>
            </Button>
            
            <Button variant="outline" className="h-auto p-4 text-left justify-start">
              <div>
                <div className="font-semibold">Security</div>
                <div className="text-sm text-muted-foreground">Your money's safety</div>
              </div>
            </Button>
            
            <Button variant="outline" className="h-auto p-4 text-left justify-start">
              <div>
                <div className="font-semibold">Withdrawals</div>
                <div className="text-sm text-muted-foreground">Early exit options</div>
              </div>
            </Button>
          </div>
        </div>
      </section>
    </div>
  );
};

export default FAQs;
