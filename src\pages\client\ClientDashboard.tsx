
import { Card } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { TrendingUp, Bell, DollarSign, Calendar } from "lucide-react";
import { <PERSON><PERSON><PERSON>, Pie, Cell, ResponsiveContainer, Tooltip } from "recharts";

const portfolioData = [
  { name: "Fixed Deposit", value: 450000, color: "#8884d8" },
  { name: "Mutual Funds", value: 200000, color: "#82ca9d" },
  { name: "<PERSON><PERSON>", value: 100000, color: "#ffc658" },
];

const ClientDashboard = () => {
  return (
    <div className="space-y-8">
      <header className="flex justify-between items-center">
        <div>
          <h1 className="text-4xl font-bold text-primary">My Portfolio</h1>
          <p className="text-muted-foreground">Track your investments and returns</p>
        </div>
        <Button>
          <Bell className="h-4 w-4 mr-2" />
          Notifications (1)
        </Button>
      </header>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Card className="p-6">
          <div className="flex items-start justify-between">
            <div>
              <p className="text-sm text-muted-foreground">Total Investment</p>
              <h2 className="text-3xl font-bold">₹7,50,000</h2>
              <p className="text-green-600 text-sm">+8.5% returns</p>
            </div>
            <div className="p-3 bg-green-100 rounded-full">
              <DollarSign className="h-6 w-6 text-green-600" />
            </div>
          </div>
        </Card>

        <Card className="p-6">
          <div className="flex items-start justify-between">
            <div>
              <p className="text-sm text-muted-foreground">Current Value</p>
              <h2 className="text-3xl font-bold">₹8,14,250</h2>
              <p className="text-green-600 text-sm">₹64,250 profit</p>
            </div>
            <div className="p-3 bg-blue-100 rounded-full">
              <TrendingUp className="h-6 w-6 text-blue-600" />
            </div>
          </div>
        </Card>

        <Card className="p-6">
          <div className="flex items-start justify-between">
            <div>
              <p className="text-sm text-muted-foreground">Next Maturity</p>
              <h2 className="text-3xl font-bold">₹2,50,000</h2>
              <p className="text-orange-600 text-sm">In 45 days</p>
            </div>
            <div className="p-3 bg-orange-100 rounded-full">
              <Calendar className="h-6 w-6 text-orange-600" />
            </div>
          </div>
        </Card>
      </div>

      {/* Portfolio Distribution and Recent Transactions */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card className="p-6">
          <h3 className="text-lg font-semibold mb-4">Portfolio Distribution</h3>
          <div className="h-[300px] w-full">
            <ResponsiveContainer width="100%" height="100%">
              <PieChart>
                <Pie
                  data={portfolioData}
                  cx="50%"
                  cy="50%"
                  outerRadius={80}
                  dataKey="value"
                  label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                >
                  {portfolioData.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={entry.color} />
                  ))}
                </Pie>
                <Tooltip formatter={(value) => [`₹${(value as number).toLocaleString()}`, 'Amount']} />
              </PieChart>
            </ResponsiveContainer>
          </div>
        </Card>

        <Card className="p-6">
          <h3 className="text-lg font-semibold mb-4">My Investments</h3>
          <div className="space-y-4">
            {[
              { scheme: "Fixed Deposit Plus", amount: "₹4,50,000", maturity: "Mar 15, 2024", status: "Active" },
              { scheme: "Growth Bonds", amount: "₹2,00,000", maturity: "Jun 20, 2024", status: "Active" },
              { scheme: "Flexible Savings", amount: "₹1,00,000", maturity: "Dec 10, 2024", status: "Active" },
            ].map((investment, i) => (
              <div key={i} className="flex items-center justify-between p-4 border rounded-lg">
                <div>
                  <p className="font-medium">{investment.scheme}</p>
                  <p className="text-sm text-muted-foreground">Maturity: {investment.maturity}</p>
                </div>
                <div className="text-right">
                  <p className="font-medium">{investment.amount}</p>
                  <span className="inline-block px-2 py-1 text-xs bg-green-100 text-green-600 rounded">
                    {investment.status}
                  </span>
                </div>
              </div>
            ))}
          </div>
          <Button className="w-full mt-4" variant="outline">
            View All Investments
          </Button>
        </Card>
      </div>

      {/* Upcoming Maturity Alert */}
      <Card className="p-6 border-orange-200 bg-orange-50">
        <div className="flex items-start space-x-4">
          <div className="p-2 bg-orange-100 rounded-full">
            <Bell className="h-6 w-6 text-orange-600" />
          </div>
          <div className="flex-1">
            <h3 className="text-lg font-semibold text-orange-800">Upcoming Maturity</h3>
            <p className="text-orange-700 mb-4">
              Your Fixed Deposit Plus investment of ₹2,50,000 will mature on March 15, 2024. 
              Please confirm your payout preference.
            </p>
            <div className="flex space-x-3">
              <Button size="sm">Confirm Payout</Button>
              <Button size="sm" variant="outline">Reinvest</Button>
            </div>
          </div>
        </div>
      </Card>
    </div>
  );
};

export default ClientDashboard;
