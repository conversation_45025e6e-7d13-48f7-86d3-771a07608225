
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { CheckCir<PERSON>, Eye } from "lucide-react";

const ClientInvestments = () => {
  const investments = [
    { id: 1, scheme: "Gold Plan", amount: 50000, startDate: "2024-01-15", maturityDate: "2025-01-15", status: "Active", monthlyPayout: 4200, lastPayout: "2024-01-01" },
    { id: 2, scheme: "Silver Plan", amount: 25000, startDate: "2024-02-01", maturityDate: "2025-02-01", status: "Active", monthlyPayout: 2100, lastPayout: "2024-01-01" },
    { id: 3, scheme: "Platinum Plan", amount: 100000, startDate: "2023-12-01", maturityDate: "2024-12-01", status: "Matured", monthlyPayout: 0, lastPayout: "2024-12-01" },
  ];

  return (
    <div className="space-y-6">
      <h1 className="text-3xl font-bold">My Investments</h1>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Investment</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">₹1,75,000</div>
            <p className="text-xs text-muted-foreground">Across all schemes</p>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Active Investments</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">2</div>
            <p className="text-xs text-muted-foreground">Currently running</p>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Monthly Returns</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">₹6,300</div>
            <p className="text-xs text-muted-foreground">Expected this month</p>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Matured Value</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">₹1,15,000</div>
            <p className="text-xs text-muted-foreground">From completed investments</p>
          </CardContent>
        </Card>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Investment Details</CardTitle>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Scheme</TableHead>
                <TableHead>Investment Amount</TableHead>
                <TableHead>Start Date</TableHead>
                <TableHead>Maturity Date</TableHead>
                <TableHead>Monthly Payout</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {investments.map((investment) => (
                <TableRow key={investment.id}>
                  <TableCell className="font-medium">{investment.scheme}</TableCell>
                  <TableCell>₹{investment.amount.toLocaleString()}</TableCell>
                  <TableCell>{investment.startDate}</TableCell>
                  <TableCell>{investment.maturityDate}</TableCell>
                  <TableCell>
                    {investment.monthlyPayout > 0 ? `₹${investment.monthlyPayout.toLocaleString()}` : "N/A"}
                  </TableCell>
                  <TableCell>
                    <Badge variant={investment.status === "Active" ? "default" : "secondary"}>
                      {investment.status}
                    </Badge>
                  </TableCell>
                  <TableCell>
                    <div className="flex space-x-2">
                      <Button variant="outline" size="sm">
                        <Eye className="h-4 w-4" />
                      </Button>
                      {investment.status === "Active" && (
                        <Button variant="outline" size="sm">
                          <CheckCircle className="h-4 w-4" />
                        </Button>
                      )}
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Recent Payouts</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {[
              { date: "2024-01-01", scheme: "Gold Plan", amount: 4200, status: "Received" },
              { date: "2024-01-01", scheme: "Silver Plan", amount: 2100, status: "Received" },
              { date: "2023-12-01", scheme: "Gold Plan", amount: 4200, status: "Received" },
              { date: "2023-12-01", scheme: "Platinum Plan", amount: 115000, status: "Received", type: "Maturity" },
            ].map((payout, index) => (
              <div key={index} className="flex items-center justify-between p-4 border rounded-lg">
                <div>
                  <h3 className="font-semibold">{payout.scheme}</h3>
                  <p className="text-sm text-muted-foreground">
                    {payout.date} {payout.type && `(${payout.type})`}
                  </p>
                </div>
                <div className="text-right">
                  <div className="font-semibold text-green-600">₹{payout.amount.toLocaleString()}</div>
                  <Badge variant="outline" className="text-xs">
                    {payout.status}
                  </Badge>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default ClientInvestments;
