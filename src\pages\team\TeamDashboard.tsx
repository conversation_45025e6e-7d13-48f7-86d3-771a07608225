
import { Card } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Users, TrendingUp, <PERSON>, DollarSign } from "lucide-react";
import { Line<PERSON>hart, Line, ResponsiveContainer, <PERSON>Axis, <PERSON>A<PERSON><PERSON>, Tooltip } from "recharts";

const teamData = [
  { name: "<PERSON>", value: 450000 },
  { name: "Feb", value: 520000 },
  { name: "<PERSON>", value: 680000 },
  { name: "Apr", value: 590000 },
  { name: "May", value: 750000 },
  { name: "<PERSON>", value: 820000 },
];

const TeamDashboard = () => {
  return (
    <div className="space-y-8">
      <header className="flex justify-between items-center">
        <div>
          <h1 className="text-4xl font-bold text-primary">Team Dashboard</h1>
          <p className="text-muted-foreground">Manage your referred clients and their investments</p>
        </div>
        <div className="flex items-center space-x-4">
          <Button variant="outline">
            <Bell className="h-4 w-4 mr-2" />
            Alerts (2)
          </Button>
        </div>
      </header>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card className="p-6">
          <div className="flex items-start justify-between">
            <div>
              <p className="text-sm text-muted-foreground">My Clients</p>
              <h2 className="text-3xl font-bold">43</h2>
              <p className="text-green-600 text-sm">+5 this month</p>
            </div>
            <div className="p-3 bg-blue-100 rounded-full">
              <Users className="h-6 w-6 text-blue-600" />
            </div>
          </div>
        </Card>

        <Card className="p-6">
          <div className="flex items-start justify-between">
            <div>
              <p className="text-sm text-muted-foreground">Total Investments</p>
              <h2 className="text-3xl font-bold">₹82L</h2>
              <p className="text-green-600 text-sm">+12% growth</p>
            </div>
            <div className="p-3 bg-green-100 rounded-full">
              <DollarSign className="h-6 w-6 text-green-600" />
            </div>
          </div>
        </Card>

        <Card className="p-6">
          <div className="flex items-start justify-between">
            <div>
              <p className="text-sm text-muted-foreground">Commission Earned</p>
              <h2 className="text-3xl font-bold">₹24,500</h2>
              <p className="text-blue-600 text-sm">This month</p>
            </div>
            <div className="p-3 bg-purple-100 rounded-full">
              <TrendingUp className="h-6 w-6 text-purple-600" />
            </div>
          </div>
        </Card>

        <Card className="p-6">
          <div className="flex items-start justify-between">
            <div>
              <p className="text-sm text-muted-foreground">Pending Approvals</p>
              <h2 className="text-3xl font-bold">3</h2>
              <p className="text-orange-600 text-sm">Need attention</p>
            </div>
            <div className="p-3 bg-orange-100 rounded-full">
              <Bell className="h-6 w-6 text-orange-600" />
            </div>
          </div>
        </Card>
      </div>

      {/* Charts and Recent Activity */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card className="p-6">
          <h3 className="text-lg font-semibold mb-4">My Team Performance</h3>
          <div className="h-[300px] w-full">
            <ResponsiveContainer width="100%" height="100%">
              <LineChart data={teamData}>
                <XAxis dataKey="name" stroke="#888888" />
                <YAxis stroke="#888888" />
                <Tooltip formatter={(value) => [`₹${(value as number / 100000).toFixed(1)}L`, 'Investment']} />
                <Line
                  type="monotone"
                  dataKey="value"
                  stroke="#8884d8"
                  strokeWidth={3}
                  dot={{ fill: '#8884d8', strokeWidth: 2, r: 6 }}
                />
              </LineChart>
            </ResponsiveContainer>
          </div>
        </Card>

        <Card className="p-6">
          <h3 className="text-lg font-semibold mb-4">Recent Client Activity</h3>
          <div className="space-y-4">
            {[
              { name: "Rahul Verma", action: "New Investment", amount: "₹1,50,000", time: "2 hours ago" },
              { name: "Priya Singh", action: "Profile Updated", amount: "Pending Approval", time: "4 hours ago" },
              { name: "Amit Kumar", action: "Maturity Alert", amount: "₹75,000", time: "6 hours ago" },
              { name: "Neha Patel", action: "New Registration", amount: "Pending Approval", time: "1 day ago" },
            ].map((activity, i) => (
              <div key={i} className="flex items-center justify-between p-3 hover:bg-gray-50 rounded-lg">
                <div className="flex items-center space-x-3">
                  <div className="h-10 w-10 rounded-full bg-primary text-white flex items-center justify-center">
                    {activity.name.charAt(0)}
                  </div>
                  <div>
                    <p className="font-medium">{activity.name}</p>
                    <p className="text-sm text-muted-foreground">{activity.action}</p>
                  </div>
                </div>
                <div className="text-right">
                  <p className="font-medium">{activity.amount}</p>
                  <p className="text-sm text-muted-foreground">{activity.time}</p>
                </div>
              </div>
            ))}
          </div>
        </Card>
      </div>
    </div>
  );
};

export default TeamDashboard;
