
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>itle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Check<PERSON><PERSON><PERSON>, Clock, Users, Award } from "lucide-react";

const StockTraining = () => {
  return (
    <div className="min-h-screen">
      {/* Hero Section */}
      <section className="bg-gradient-to-r from-primary to-primary/80 text-white py-20">
        <div className="container mx-auto px-6 text-center">
          <h1 className="text-4xl md:text-6xl font-bold mb-6">
            Master Stock Trading with Expert Guidance
          </h1>
          <p className="text-xl mb-8 max-w-3xl mx-auto">
            Transform your financial future with our comprehensive stock trading training program. 
            Learn from industry experts and develop winning strategies.
          </p>
          <Button size="lg" className="bg-white text-primary hover:bg-gray-100">
            Enroll Now - ₹9,999
          </Button>
        </div>
      </section>

      {/* Course Overview */}
      <section className="py-16">
        <div className="container mx-auto px-6">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold mb-4">Course Overview</h2>
            <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
              Our comprehensive training program covers everything from basics to advanced trading strategies
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-12">
            <Card>
              <CardHeader className="text-center">
                <Clock className="h-12 w-12 mx-auto mb-4 text-primary" />
                <CardTitle>Duration</CardTitle>
              </CardHeader>
              <CardContent className="text-center">
                <p className="text-2xl font-bold">8 Weeks</p>
                <p className="text-muted-foreground">Intensive Training</p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="text-center">
                <Users className="h-12 w-12 mx-auto mb-4 text-primary" />
                <CardTitle>Format</CardTitle>
              </CardHeader>
              <CardContent className="text-center">
                <p className="text-2xl font-bold">Live Online</p>
                <p className="text-muted-foreground">Interactive Sessions</p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="text-center">
                <Award className="h-12 w-12 mx-auto mb-4 text-primary" />
                <CardTitle>Certification</CardTitle>
              </CardHeader>
              <CardContent className="text-center">
                <p className="text-2xl font-bold">Included</p>
                <p className="text-muted-foreground">Industry Recognized</p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="text-center">
                <CheckCircle className="h-12 w-12 mx-auto mb-4 text-primary" />
                <CardTitle>Support</CardTitle>
              </CardHeader>
              <CardContent className="text-center">
                <p className="text-2xl font-bold">Lifetime</p>
                <p className="text-muted-foreground">Expert Guidance</p>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* Course Curriculum */}
      <section className="py-16 bg-muted/30">
        <div className="container mx-auto px-6">
          <h2 className="text-3xl font-bold text-center mb-12">What You'll Learn</h2>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            <div className="space-y-6">
              <div className="flex items-start space-x-4">
                <CheckCircle className="h-6 w-6 text-green-500 mt-1" />
                <div>
                  <h3 className="font-semibold text-lg">Stock Market Fundamentals</h3>
                  <p className="text-muted-foreground">Understanding market mechanics, terminology, and basic concepts</p>
                </div>
              </div>

              <div className="flex items-start space-x-4">
                <CheckCircle className="h-6 w-6 text-green-500 mt-1" />
                <div>
                  <h3 className="font-semibold text-lg">Technical Analysis</h3>
                  <p className="text-muted-foreground">Chart patterns, indicators, and trading signals</p>
                </div>
              </div>

              <div className="flex items-start space-x-4">
                <CheckCircle className="h-6 w-6 text-green-500 mt-1" />
                <div>
                  <h3 className="font-semibold text-lg">Fundamental Analysis</h3>
                  <p className="text-muted-foreground">Company valuation, financial statements, and market research</p>
                </div>
              </div>

              <div className="flex items-start space-x-4">
                <CheckCircle className="h-6 w-6 text-green-500 mt-1" />
                <div>
                  <h3 className="font-semibold text-lg">Risk Management</h3>
                  <p className="text-muted-foreground">Portfolio management and risk mitigation strategies</p>
                </div>
              </div>
            </div>

            <div className="space-y-6">
              <div className="flex items-start space-x-4">
                <CheckCircle className="h-6 w-6 text-green-500 mt-1" />
                <div>
                  <h3 className="font-semibold text-lg">Trading Psychology</h3>
                  <p className="text-muted-foreground">Emotional control and mindset for successful trading</p>
                </div>
              </div>

              <div className="flex items-start space-x-4">
                <CheckCircle className="h-6 w-6 text-green-500 mt-1" />
                <div>
                  <h3 className="font-semibold text-lg">Options & Derivatives</h3>
                  <p className="text-muted-foreground">Advanced trading instruments and strategies</p>
                </div>
              </div>

              <div className="flex items-start space-x-4">
                <CheckCircle className="h-6 w-6 text-green-500 mt-1" />
                <div>
                  <h3 className="font-semibold text-lg">Live Trading Sessions</h3>
                  <p className="text-muted-foreground">Real-time market analysis and trading practice</p>
                </div>
              </div>

              <div className="flex items-start space-x-4">
                <CheckCircle className="h-6 w-6 text-green-500 mt-1" />
                <div>
                  <h3 className="font-semibold text-lg">Portfolio Building</h3>
                  <p className="text-muted-foreground">Creating and managing diversified investment portfolios</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Inquiry Form */}
      <section className="py-16">
        <div className="container mx-auto px-6">
          <div className="max-w-2xl mx-auto">
            <h2 className="text-3xl font-bold text-center mb-8">Get Started Today</h2>
            
            <Card>
              <CardHeader>
                <CardTitle>Course Inquiry Form</CardTitle>
              </CardHeader>
              <CardContent>
                <form className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="name">Full Name</Label>
                      <Input id="name" placeholder="Enter your full name" />
                    </div>
                    <div>
                      <Label htmlFor="email">Email</Label>
                      <Input id="email" type="email" placeholder="Enter your email" />
                    </div>
                  </div>
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="phone">Phone</Label>
                      <Input id="phone" placeholder="Enter your phone number" />
                    </div>
                    <div>
                      <Label htmlFor="experience">Trading Experience</Label>
                      <select className="w-full p-2 border rounded-md">
                        <option>Beginner</option>
                        <option>Intermediate</option>
                        <option>Advanced</option>
                      </select>
                    </div>
                  </div>
                  
                  <div>
                    <Label htmlFor="message">Questions/Comments</Label>
                    <Textarea id="message" placeholder="Any specific questions about the course?" rows={4} />
                  </div>
                  
                  <Button className="w-full" size="lg">
                    Submit Inquiry
                  </Button>
                </form>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>
    </div>
  );
};

export default StockTraining;
